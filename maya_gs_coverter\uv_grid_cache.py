# Import built-in modules
from collections import defaultdict
from collections import deque

# Import third-party modules
import maya.api.OpenMaya as om
import maya.cmds as cmds
import numpy as np

# Import local modules
import maya_gs_coverter.utils.utils as utils


class UVGridCache(object):
    _instance = None
    _uv_grid_dict = {}

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(UVGridCache, cls).__new__(cls)
        return cls._instance

    def get_uv_grid(self, mesh, uv_tolerance=0.0001, force_update=False):
        """
        获取 mesh 的 UV 邻接二维数组，并缓存结果。

        Args:
            mesh (str): mesh 的 transform 或 shape 名称。
            uv_tolerance (float, optional): 行分组的 V 值容差。默认 0.0001。
            force_update (bool, optional): 是否强制刷新缓存。默认 False。

        Returns:
            list[list[dict]]: uv_grid[row][col]，元素形如 {'uv': (u, v), 'vtx': idx}
        """
        # 统一用shape名做key
        # TODO
        # 假设删除了，然后创建新的，获取mesh就会是上一个mesh，待修正
        if cmds.nodeType(mesh) != "mesh":
            shapes = cmds.listRelatives(mesh, shapes=True, fullPath=True)
            if not shapes:
                raise RuntimeError("找不到mesh shape节点")
            mesh_shape = shapes[0]
        else:
            mesh_shape = mesh

        key = mesh_shape
        if not force_update:
            if key in self._uv_grid_dict:
                return self._uv_grid_dict[key]

        # 计算并缓存
        uv_grid = self._compute_uv_grid_with_adjacency(mesh_shape, uv_tolerance)
        self._uv_grid_dict[key] = uv_grid
        return uv_grid

    def _compute_uv_grid_with_adjacency(self, mesh, uv_tolerance):
        # 1. 获取所有顶点的UV
        mesh_fn = self.get_mesh_fn(mesh)
        u_array, v_array = mesh_fn.getUVs()
        uv_sets = mesh_fn.getUVSetNames()
        counts, indices = mesh_fn.getAssignedUVs()
        if uv_sets:
            counts, indices = mesh_fn.getAssignedUVs(uv_sets[0])
        else:
            print("没有UV集")
        # 构建UV邻接表
        uv_adj = defaultdict(set)
        idx = 0
        for face_id in range(mesh_fn.numPolygons):
            vtx_ids = mesh_fn.getPolygonVertices(face_id)
            uv_ids = indices[idx : idx + len(vtx_ids)]
            for i, uv_id in enumerate(uv_ids):
                next_uv_id = uv_ids[(i + 1) % len(uv_ids)]
                prev_uv_id = uv_ids[(i - 1) % len(uv_ids)]
                uv_adj[uv_id].add(next_uv_id)
                uv_adj[uv_id].add(prev_uv_id)
            idx += len(vtx_ids)

        uv_id_to_vtx_id = {}
        idx = 0
        for face_id in range(mesh_fn.numPolygons):
            vtx_ids = mesh_fn.getPolygonVertices(face_id)
            uv_ids = indices[idx : idx + len(vtx_ids)]
            for vtx_id, uv_id in zip(vtx_ids, uv_ids):
                uv_id_to_vtx_id[uv_id] = vtx_id
            idx += len(vtx_ids)
        # 找到四个端点
        uv_points = np.array(list(zip(u_array, v_array)))
        u_vals, v_vals = uv_points[:, 0], uv_points[:, 1]
        left_top = np.argmin(u_vals - v_vals)
        right_top = np.argmax(u_vals + v_vals)
        left_bottom = np.argmin(u_vals + v_vals)
        right_bottom = np.argmax(u_vals - v_vals)
        corner_uv_ids = [left_top, right_top, left_bottom, right_bottom]

        grid = self.bfs_uv_grid(left_top, uv_adj, u_array, v_array, uv_tolerance, uv_id_to_vtx_id)
        # 检查柱形缝合
        remove_last_col = True
        for row in grid:
            if row[0] is None or row[-1] is None:
                remove_last_col = False
                break
            u0 = row[0]["uv"][0]
            u1 = row[-1]["uv"][0]
            if abs(u0 - u1) > uv_tolerance:
                remove_last_col = False
                break
        if remove_last_col:
            for i in range(len(grid)):
                grid[i] = grid[i][:-1]

        row_lengths = [len([x for x in row if x is not None]) for row in grid]
        if len(set(row_lengths)) != 1:
            print("每一行长度不一致")
            return None
        col_lengths = [len([row[c] for row in grid if row[c] is not None]) for c in range(len(grid[0]))]
        if len(set(col_lengths)) != 1:
            print("每一列长度不一致")
            return None

        return grid

    # 以左上角为起点，BFS遍历UV网格
    def bfs_uv_grid(self, start_uv_id, uv_adj, u_array, v_array, uv_tolerance, uv_id_to_vtx_id):
        visited = set()
        grid = []
        queue = deque()
        queue.append((start_uv_id, 0, 0))  # (uv_id, row, col)
        uv_pos_to_rc = {start_uv_id: (0, 0)}
        visited.add(start_uv_id)
        uv_id_to_pos = {start_uv_id: (u_array[start_uv_id], v_array[start_uv_id])}
        # 记录每个row的uv_id
        row_dict = defaultdict(dict)
        row_dict[0][0] = start_uv_id

        while queue:
            uv_id, row, col = queue.popleft()
            for nbr in uv_adj[uv_id]:
                if nbr in visited:
                    continue
                # 判断是同一行还是同一列
                du = abs(u_array[nbr] - u_array[uv_id])
                dv = abs(v_array[nbr] - v_array[uv_id])
                if du < uv_tolerance and dv > uv_tolerance:
                    # 同一列，行变
                    next_row, next_col = row + (1 if v_array[nbr] < v_array[uv_id] else -1), col
                elif dv < uv_tolerance and du > uv_tolerance:
                    # 同一行，列变
                    next_row, next_col = row, col + (1 if u_array[nbr] > u_array[uv_id] else -1)
                else:
                    continue  # 斜对角或重合，跳过
                if (next_row, next_col) not in row_dict or nbr not in row_dict[next_row].values():
                    row_dict[next_row][next_col] = nbr
                    queue.append((nbr, next_row, next_col))
                    visited.add(nbr)
        # 组织成二维grid
        min_row = min(row_dict.keys())
        max_row = max(row_dict.keys())
        min_col = min(min(cols.keys()) for cols in row_dict.values())
        max_col = max(max(cols.keys()) for cols in row_dict.values())
        grid = []
        for r in range(min_row, max_row + 1):
            row = []
            for c in range(min_col, max_col + 1):
                uv_id = row_dict[r].get(c)
                if uv_id is not None:
                    vtx_id = uv_id_to_vtx_id.get(uv_id, None)
                    row.append({"uv": (u_array[uv_id], v_array[uv_id]), "uv_id": uv_id, "vtx": vtx_id})
                else:
                    row.append(None)
            grid.append(row)
        return grid

    def get_middle_line_points(self, mesh):
        """
        返回每一行的中点坐标，不同类型的毛发采用不同的中点计算方式。

        Args:
            mesh (str): mesh 的 transform 或 shape 名称。

        Returns:
            list[list[float]]: 3D 中点列表 [[x, y, z], ...]
        """
        uv_grid = self.get_uv_grid(mesh)
        ispoly = self.is_polygonal_hair(mesh)
        mesh_fn = self.get_mesh_fn(mesh)
        mid_points = []
        if ispoly:
            for row in uv_grid:
                pts = [om.MVector(mesh_fn.getPoint(cell["vtx"], om.MSpace.kWorld)) for cell in row]
                mid = sum(pts, om.MVector(0, 0, 0)) * (1.0 / len(pts))
                mid_points.append([mid.x, mid.y, mid.z])
        else:
            for row in uv_grid:
                left = row[0]
                right = row[-1]
                p1 = om.MVector(mesh_fn.getPoint(left["vtx"], om.MSpace.kWorld))
                p2 = om.MVector(mesh_fn.getPoint(right["vtx"], om.MSpace.kWorld))

                mid = (p1 + p2) * 0.5
                # utils.create_debug_sphere(p1, f"{left['vtx']}")
                # utils.create_debug_sphere(p2, f"{right['vtx']}")
                # utils.create_debug_sphere(mid, "mid")
                mid_points.append([mid.x, mid.y, mid.z])
        return mid_points

    def get_mesh_fn(self, mesh):
        """
        获取 MFnMesh 对象。

        Args:
            mesh (str): mesh 的 transform 或 shape 名称。

        Returns:
            om.MFnMesh: 网格函数集。
        """
        sel_list = om.MSelectionList()
        sel_list.add(mesh)
        dag_path = sel_list.getDagPath(0)
        return om.MFnMesh(dag_path)

    def clear_cache(self):
        """
        清空 UV 网格缓存。
        """
        self._uv_grid_dict.clear()

    def get_uv_transform(self, mesh, uv_tolerance=0.01):
        """
        计算如何将一个铺满的 UV(0-1) 四边形变换到当前 UV 包围盒的 MoveU、MoveV、ScaleU、ScaleV。

        平移和缩放的枢轴都使用 (0.5, 0) 作为中心点。

        Args:
            mesh (str): mesh 的 transform 或 shape 名称。
            uv_tolerance (float, optional): 行分组的 V 值容差。默认 0.01。

        Returns:
            dict: {'moveU': float, 'moveV': float, 'scaleU': float, 'scaleV': float}
        """
        uv_grid = self.get_uv_grid(mesh, uv_tolerance)
        # 收集所有uv
        all_uvs = []
        for row in uv_grid:
            for cell in row:
                all_uvs.append(cell["uv"])
        if not all_uvs:
            raise RuntimeError("未找到UV数据")
        uvs_np = np.array(all_uvs)
        min_u = np.min(uvs_np[:, 0])
        max_u = np.max(uvs_np[:, 0])
        min_v = np.min(uvs_np[:, 1])
        max_v = np.max(uvs_np[:, 1])

        # 计算UV变换（委托到通用函数，逻辑保持一致）
        return utils.uv_transform_from_bounds(min_u, max_u, min_v, max_v)

    @staticmethod
    def is_polygonal_hair(mesh):
        """
        使用 UV 网格判断是否为多边形毛发（横截面为多边形）。

        条件：每一行顶点数 >= 3，且这些点在 mesh 中为环状连接。

        Args:
            mesh (str): mesh 的 transform 或 shape 名称。

        Returns:
            bool | None: 是多边形毛发返回 True，线状返回 False；无法判断返回 None。
        """
        uv_cache = UVGridCache()
        uv_grid = uv_cache.get_uv_grid(mesh)
        if not uv_grid or len(uv_grid) == 0:
            return None

        sel_list = om.MSelectionList()
        sel_list.add(mesh)
        dag_path = sel_list.getDagPath(0)
        mesh_fn = om.MFnMesh(dag_path)

        # 辅助函数：获取顶点连接的边
        def get_vertex_connected_edges(mesh_fn, vtx_id):
            it = om.MItMeshVertex(mesh_fn.object())
            it.setIndex(vtx_id)
            return it.getConnectedEdges()

        for row in uv_grid:
            group = [item["vtx"] for item in row]
            if len(group) < 3:
                continue
            connect_count = []
            for vtx_id in group:
                count = 0
                edge_ids = get_vertex_connected_edges(mesh_fn, vtx_id)
                for eid in edge_ids:
                    other_vids = mesh_fn.getEdgeVertices(eid)
                    # 只统计本组内的边
                    if all(vid in group for vid in other_vids):
                        count += 1
                connect_count.append(count)
            # 环状：每个点都至少有2条边与本组内点相连
            if all(c >= 2 for c in connect_count):
                return True
        return False


# 用法示例
# uv_grid = UVGridCache().get_uv_grid("pPlane1")
# print(uv_grid[0][0])  # {'uv': (u, v), 'vtx': idx}
