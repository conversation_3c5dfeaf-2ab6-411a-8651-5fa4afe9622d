"""Describe our module distribution to Distutils."""

# Import future modules
from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

# Import third-party modules
from setuptools import find_packages
from setuptools import setup

setup(
    name="maya_gs_coverter",
    author="<PERSON><PERSON>",
    author_email="<PERSON><PERSON>@tencent.com",
    url="https://git.woa.com/lightbox/internal/maya_gs_coverter",
    package_dir={"": "."},
    packages=find_packages("."),
    description="Convert regular type inserts to GS editable curves, still in experimental stage.",
    entry_points={},
    classifiers=[
        "Programming Language :: Python",
        "Programming Language :: Python :: 3",
    ],
    use_scm_version=True,
    setup_requires=["setuptools_scm"],
)
