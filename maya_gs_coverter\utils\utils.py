# This code is referenced from the gs_curve_tools source code, with no modifications to naming or other elements.
# Import built-in modules
import random
import string

# Import third-party modules
import maya.cmds as mc


def checkIfBezier(inputCurve):
    if mc.nodeType(mc.listRelatives(inputCurve, c=1, pa=1)) == "bezierCurve":
        origSel = mc.ls(sl=1)
        mc.select(inputCurve, r=1)
        result = mc.bezierCurveToNurbs()
        origSel.append(result[0])
        if result:
            mc.rebuildCurve(result, kr=2, kcp=1, fr=1)
        mc.select(origSel, r=1)
        return result
    return inputCurve


def attrExists(obj, attr):
    """Check if attribute exists"""
    if mc.objExists(obj):
        if mc.attributeQuery(attr, n=obj, ex=1):
            return 1
        return 0
    else:
        return 0


def resetSingleGraph(graph, target=None):
    sel = target if target else mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    curve = sel[-1]
    if mc.attributeQuery("Length", n=curve, ex=1):
        if mc.connectionInfo((curve + ".Length"), isSource=1):
            warp = mc.listConnections(curve + ".Length")
            if warp:
                if graph == "scale" or graph == "width":
                    setDouble2Attr(warp[0], "scaleCurve", [(0, 0.5), (0.333, 0.5), (0.666, 0.5), (1, 0.5)])
                if graph == "twist":
                    setDouble2Attr(warp[0], "twistCurve", [(0, 0.5), (0.333, 0.5), (0.666, 0.5), (1, 0.5)])


def resetSingleGraphValues(graph):
    sel = mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    curve = sel[-1]
    if mc.attributeQuery("Length", n=curve, ex=1):
        if mc.connectionInfo((curve + ".Length"), isSource=1):
            warp = mc.listConnections(curve + ".Length")
            if warp:
                attr = getGraphAttrName(graph)
                defaults = "0, 0.5, 1, 0.5"
                m_graph = mc.falloffCurveAttr(ccw=5, stg=0, h=220, hlc=[0.68, 0.68, 0.68])
                path = warp[0] + f".{attr}"
                mc.falloffCurveAttr((m_graph), e=1, at=path)
                mc.falloffCurveAttr((m_graph), e=1, asString=defaults)
                # TODO: 使用完毕后销毁


def getGraphAttrName(graph):
    attrname = ""
    if graph in ("scale", "width"):
        attrname = "scaleCurve"
    elif graph == "twist":
        attrname = "twistCurve"
    elif graph == "profile":
        attrname = "profileCurve"
    return attrname


def setCurveGraphAttr(graph, valueList):
    """
    给指定curve的字符串属性设置graph数据。
    """
    sel = mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    if not sel:
        raise RuntimeError("No valid target found.")
    curve = sel[-1]
    inputstring = fromDouble2ToString(valueList)
    attrname = getGraphAttrName(graph)
    # 检查属性是否存在，不存在则添加
    if not mc.attributeQuery(attrname, n=curve, ex=1):
        mc.addAttr(curve, ln=attrname, dt="string")

    mc.setAttr(curve + "." + attrname, inputstring, type="string")


def setSingleGraph(graph, valueList, target=None):
    sel = target if target else mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    if not sel:
        raise RuntimeError("No valid target found.")
    curve = sel[-1]
    if mc.attributeQuery("Length", n=curve, ex=1):
        if mc.connectionInfo((curve + ".Length"), isSource=1):
            warp = mc.listConnections(curve + ".Length")
            if warp:
                node = warp[0]
                if graph in ("scale", "width"):
                    setDouble2Attr(node, "scaleCurve", valueList)
                elif graph == "twist":
                    setDouble2Attr(node, "twistCurve", valueList)
                else:
                    raise ValueError("Unknown graph type: %s" % graph)


def setUVTransform(transform, target=None):
    sel = target if target else mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    if not sel:
        raise RuntimeError("No valid target found.")
    curve = sel[-1]
    for key, attr in transform.items():
        mc.setAttr(f"{curve}.{key}", transform[key])


def uv_transform_from_bounds(min_u, max_u, min_v, max_v):
    """Compute UV transform dict from UV bounds.

    Pivot is (0.5, 0) for both translation and scaling.
    Returns a dict: {"moveU", "moveV", "scaleU", "scaleV"}.
    """
    # Compute scales (pivot at 0.5, 0)
    scale_u = max_u - min_u  # U direction scale
    scale_v = max_v - min_v  # V direction scale

    # Compute moves (pivot at 0.5, 0)
    # U: move center to 0.5
    move_u = (min_u + max_u) / 2 - 0.5
    # V: pivot is 0, move by min_v
    move_v = min_v

    return {
        "moveU": move_u,
        "moveV": move_v,
        "scaleU": scale_u,
        "scaleV": scale_v,
    }


def setProfile(profile, target=None):
    sel = target if target else mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    curve = sel[-1]

    if mc.attributeQuery("Profile", n=curve, ex=1):
        mc.setAttr(f"{curve}.Profile", profile)


def setCurveRefine(curverifine, target=None):
    sel = target if target else mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    curve = sel[-1]

    if mc.attributeQuery("curveRefine", n=curve, ex=1):
        mc.setAttr(f"{curve}.curveRefine", curverifine)


def setDouble2Attr(node, attr, values):
    for i in range(len(values)):
        mc.setAttr(("%s.%s[%s]" % (node, attr, i)), (values[i][0]), (values[i][1]), typ="double2")


def fromStringToDouble2(inputString):
    splitPoints = inputString.split(",")
    splitPoints = filter(None, splitPoints)
    splitPoints = [float(point) for point in splitPoints]
    arrangedValues = [[splitPoints[i], splitPoints[i + 1]] for i in range(0, len(splitPoints), 2)]
    return arrangedValues


def setFlipUV(curve_name, flipuv):
    flip = 0 if flipuv else 1
    mc.setAttr(curve_name + ".flipUV", flip)


def fromDouble2ToString(inputList):
    newString = ""
    for i in range(len(inputList)):
        newString += "%s,%s," % (
            inputList[i][0],
            inputList[i][1],
        )

    return newString


def set_profile_twist(curve, twist_angle):
    if curve:
        mc.setAttr(curve + ".Twist", twist_angle)


def set_profile_intwist(curve, intwist_angle):
    if curve:
        mc.setAttr(curve + ".invTwist", intwist_angle)


def set_profile_orientation(curve, orientation):
    if curve:
        mc.setAttr(curve + ".Orientation", orientation)


def set_curve_width(curve, scale_factor, ispoly=False):
    """
    scale_factor: 缩放系数
    """
    if ispoly:
        mc.setAttr(curve + ".WidthX", scale_factor)
        mc.setAttr(curve + ".WidthZ", scale_factor)
    else:
        mc.setAttr(curve + ".Width", scale_factor)


def getAttr(obj, attr):
    """Check if obj exist, check if attr exist, return attr"""
    if mc.objExists(obj):
        if mc.attributeQuery(attr, n=obj, ex=1):
            return mc.getAttr(obj + "." + attr)
        return 0
    else:
        return 0


def create_debug_sphere(pos, name_prefix):
    # 生成随机字符串确保名称唯一
    rand_str = "".join(random.choices(string.ascii_lowercase, k=4))
    sphere_name = f"{name_prefix}_debug_{rand_str}"
    sphere = mc.polySphere(name=sphere_name, radius=0.1)[0]
    mc.move(pos[0], pos[1], pos[2], sphere, absolute=True)
    return sphere


def get_normalized_curve_positions(mid_points):
    """
    计算mid_points中每个顶点在曲线上的归一化位置（0-1）。
    返回一个列表，长度与mid_points一致，每个元素为该点的归一化位置。
    """
    # Import third-party modules
    import numpy as np

    distances = []
    for i in range(1, len(mid_points)):
        p1 = np.array(mid_points[i - 1])
        p2 = np.array(mid_points[i])
        dist = np.linalg.norm(p2 - p1)
        distances.append(dist)

    cum_distances = [0]
    for d in distances:
        cum_distances.append(cum_distances[-1] + d)

    total_length = cum_distances[-1]
    if total_length == 0:
        return [0.0 for _ in mid_points]

    normalized_positions = [cd / total_length for cd in cum_distances]
    return normalized_positions



def init_progress_ui(progress_bar, progress_text, total):
    """Initialize progress UI for both Qt widgets and Maya UI controls."""
    if not progress_bar and not progress_text:
        return
    try:
        if progress_bar:
            if hasattr(progress_bar, "setMaximum"):
                progress_bar.setMaximum(total)
                progress_bar.setValue(0)
            else:
                mc.progressBar(progress_bar, edit=True, maxValue=total, progress=0)
        if progress_text:
            if hasattr(progress_text, "setText"):
                progress_text.setText(f"已处理 0 / {total}")
            else:
                mc.text(progress_text, edit=True, label=f"已处理 0 / {total}")
    except Exception:
        pass


def update_progress_ui(progress_bar, progress_text, progress, total):
    """Update progress UI safely."""
    if not progress_bar and not progress_text:
        return
    try:
        if progress_bar:
            if hasattr(progress_bar, "setValue"):
                progress_bar.setValue(progress)
            else:
                mc.progressBar(progress_bar, edit=True, progress=progress)
        if progress_text:
            if hasattr(progress_text, "setText"):
                progress_text.setText(f"已处理 {progress} / {total}")
            else:
                mc.text(progress_text, edit=True, label=f"已处理 {progress} / {total}")
    except Exception:
        pass


def finish_progress_ui(progress_bar, progress_text, total):
    """Finish progress UI to 100%."""
    if not progress_bar and not progress_text:
        return
    try:
        if progress_bar:
            if hasattr(progress_bar, "setValue"):
                progress_bar.setValue(total)
            else:
                mc.progressBar(progress_bar, edit=True, progress=total)
        if progress_text:
            if hasattr(progress_text, "setText"):
                progress_text.setText(f"已处理 {total} / {total}")
            else:
                mc.text(progress_text, edit=True, label=f"已处理 {total} / {total}")
    except Exception:
        pass


def set_valid_uv_set(mesh_transform):
    """Switch mesh to the first UV set that actually contains UVs.
    Avoids Maya's auto-created empty 'map1' after duplicate.
    """
    try:
        shapes = mc.listRelatives(mesh_transform, shapes=True, noIntermediate=True, fullPath=True) or []
        if not shapes:
            return
        shape = shapes[0]
        uv_sets = mc.polyUVSet(shape, q=True, allUVSets=True) or []
        for uv in uv_sets:
            try:
                mc.polyUVSet(shape, e=True, currentUVSet=True, uvSet=uv)
                uvc = mc.polyEvaluate(shape, uvcoord=True)
                if uvc and uvc > 0:
                    return  # found a valid UV set
            except Exception:
                continue
    except Exception:
        pass
