# This code is referenced from the gs_curve_tools source code, with no modifications to naming or other elements.
def lerp(x, y0, y1, x0=0, x1=1):
    """Returns the value between y0 and y1 based on x in range of x0 and x1

    https://en.wikipedia.org/wiki/Linear_interpolation"""
    return float((y0 * (x1 - x) + y1 * (x - x0)) / (x1 - x0))


def quad(y1, y2, y3, fPoint, x1=0, x2=0.5, x3=1):
    """Takes three points (y1, y2, y3) and returns a point on computed curve with fPoint"""
    A = (x2 * (y1 - y3) + x3 * (y2 - y1)) / ((x1 - x2) * (x1 - x3) * (x2 - x3))
    B = (y2 - y1) / (x2 - x1) - A * (x1 + x2)
    C = y1
    return A * fPoint**2 + B * fPoint + C


def dot(v1, v2):
    """Dot product of two QVector2D"""
    return QtGui.QVector2D.dotProduct(v1, v2)


def angleDiff(a1, a2):
    """Find an absolute angle difference within 360 degrees"""
    a1 = a1 % 360
    a2 = a2 % 360
    return abs(a1) - abs(a2)
