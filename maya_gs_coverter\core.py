# This code is referenced from the gs_curve_tools source code, with no modifications to naming or other elements.

# Import third-party modules
import maya.cmds as mc

# Import local modules
import maya_gs_coverter.utils.gs_math as mt
import maya_gs_coverter.utils.utils as utils


class Attributes:
    attrList = {
        "lengthDivisions",
        "widthDivisions",
        "Orientation",
        "Twist",
        "invTwist",
        "Width",
        "WidthX",
        "WidthZ",
        "LengthLock",
        "Length",
        "Taper",
        "Profile",
        "curveRefine",
        "curveSmooth",
        "reverseNormals",
        "surfaceNormals",
        "flipUV",
        "moveU",
        "moveV",
        "rotateUV",
        "rotateRootUV",
        "rotateTipUV",
        "scaleU",
        "scaleV",
        "solidify",
        "solidifyThickness",
        "solidifyDivisions",
        "solidifyScaleX",
        "solidifyScaleY",
        "solidifyOffset",
        "solidifyNormals",
        "AxisFlip",
        "Offset",
        "lineWidth",
        "samplingAccuracy",
        "Magnitude",
        "profileSmoothing",
        "profileMagnitude",
    }
    uvAttr = {
        "moveU",
        "moveV",
        "rotateUV",
        "rotateRootUV",
        "rotateTipUV",
        "scaleU",
        "scaleV",
    }
    checkBoxes = {
        "flipUV",
        "LengthLock",
        "reverseNormals",
        "solidify",
        "Axis",
        "AxisFlip",
    }
    multiInst = {
        "twistCurve",
        "scaleCurve",
    }
    graphAttributes = {
        "twistCurve",
        "scaleCurve",
        "profileCurve",
    }

    def getAttr(self, inputCurve, excludeUV=False, excludeCheckboxes=False, manualExclude=False):
        getAttr = dict()
        for attr in self.attrList:
            if excludeUV:
                if attr in self.uvAttr:
                    continue
            elif excludeCheckboxes:
                if attr in self.checkBoxes:
                    continue
            if manualExclude and attr in manualExclude:
                continue
            try:
                getAttr[attr] = mc.getAttr(inputCurve + "." + attr)
            except BaseException:
                pass

        return getAttr

    def getCheckboxes(self, inputCurve):
        checkboxes = dict()
        for attr in self.checkBoxes:
            try:
                checkboxes[attr] = mc.getAttr(inputCurve + "." + attr)
            except BaseException:
                pass

        return checkboxes

    def getUVs(self, inputCurve):
        getUVs = dict()
        for attr in self.uvAttr:
            try:
                getUVs[attr] = mc.getAttr(inputCurve + "." + attr)
            except BaseException:
                pass

        return getUVs

    @staticmethod
    def setAttr(inputCurve, inputDict, exclude=None):
        for attr in inputDict:
            if exclude:
                if attr in exclude:
                    continue
            try:
                mc.setAttr(inputCurve + "." + attr, inputDict[attr])
            except BaseException:
                pass

    def deleteAttr(self, inputCurve):
        attrDict = self.attrList
        for attr in attrDict:
            if mc.attributeQuery(attr, n=inputCurve, ex=1):
                mc.deleteAttr(inputCurve + "." + attr)


attributes = Attributes()


class Create:
    def __init__(self):
        self.globalThickness = mc.optionVar(q="GSCT_globalCurveThickness")
        self.sf = 1.0
        self.nurbsTesselate = None
        self.curveWarp = None
        self.polyMoveUV_mid = None
        self.polyMoveUV_tip = None
        self.polyMoveUV_root = None
        self.solidifyNode = None
        self.solidifyChoice = None
        self.extrude = None
        self.lattice = None
        self.twist = None
        self.magnExpr = None
        self.scaleExpr = None
        self.UVexpr = None
        self.polyNormalNode = None
        self.polySoftEdge = None

    def currentLayerInd(self):
        return 2

    def initialize(self):
        self.__init__()
        self.sf = 1.0
        ind = self.currentLayerInd()
        layer = "curveGrp_%s_Curve" % ind
        if not mc.objExists(layer):
            # 创建 display layer
            mc.createDisplayLayer(name=layer, empty=True)

    def new(self, mode, hk=None):
        self.initialize()
        # CONTROLS["warpSwitch"].isChecked()
        warpRadio = True
        if warpRadio or hk:
            mode = mode + 2
        pathCurve = mc.curve(
            d=3,
            p=[
                (0, -0.0001, 0),
                (1.666667 * self.sf, 0, 0),
                (5 * self.sf, 0, 0),
                (10 * self.sf, 0, 0),
                (
                    15 * self.sf,
                    0,
                    0,
                ),
                (18.333333 * self.sf, 0, 0),
                (20 * self.sf, 0, 0),
            ],
            k=[
                0,
                0,
                0,
                1,
                2,
                3,
                4,
                4,
                4,
            ],
            n="pathCurve_inst#",
        )
        mc.rebuildCurve(pathCurve, kr=2)
        finalCurve = []
        if mode == 0:
            finalCurve = self.extrudeCard(pathCurve, False)
        else:
            if mode == 1:
                finalCurve = self.extrudeTube(pathCurve, False)
            else:
                if mode == 2:
                    finalCurve = self.warpCard(pathCurve, False)
                else:
                    if mode == 3:
                        finalCurve = self.warpTube(pathCurve, False)
                    mc.select(finalCurve)

    def extrudeCard(self, pathInst, auto=True):
        self.pathInst = mc.filterExpand(pathInst, sm=9)[0]
        if not self.pathInst:
            print("Select nurbs or bezier curve")
            return 0
        spans = mc.getAttr(self.pathInst + ".spans")
        self.ldiv = 10
        self.refine = 50
        if auto:
            self.ldiv = 8 + spans * 2
            self.refine = 50 + spans * 2
        try:
            mc.disconnectAttr(self.pathInst + ".sx", self.pathInst + ".sy")
        except BaseException:
            pass

        try:
            mc.disconnectAttr(self.pathInst + ".sx", self.pathInst + ".sz")
        except BaseException:
            pass

        scaleFactor = self.sf
        if mc.attributeQuery("scaleFactor", n=(self.pathInst), ex=1):
            # CONTROLS["keepCurveAttributes"].isChecked()
            if True:
                scaleFactor = mc.getAttr(self.pathInst + ".scaleFactor")
        self.profileInst = mc.curve(
            p=[
                (
                    0,
                    0,
                    -2.5 * scaleFactor,
                ),
                (0, 0.5 * scaleFactor, -1.666667 * scaleFactor),
                (0, 1 * scaleFactor, 0),
                (
                    0,
                    0.5 * scaleFactor,
                    1.666667 * scaleFactor,
                ),
                (0, 0, 2.5 * scaleFactor),
            ],
            k=[0, 0, 0, 1, 2, 2, 2],
            d=3,
            n="profileCurve_inst#",
        )
        mc.makeIdentity((self.pathInst), n=0, s=1, r=1, t=1, apply=True, pn=1)
        curveOrigin = mc.pointPosition((self.pathInst + ".cv[0]"), w=1)
        mc.xform((self.pathInst), ws=1, piv=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        mc.move(0, 0, 0, (self.pathInst), rpr=1, ws=1)
        mc.makeIdentity((self.pathInst), n=0, s=1, r=1, t=1, apply=True, pn=1)
        mc.nurbsToPolygonsPref(polyType=1, vType=1, format=2, uType=1, vNumber=10, uNumber=3)
        self.hairCard = mc.extrude(
            (self.profileInst),
            (self.pathInst),
            upn=1,
            ch=True,
            rotation=0,
            ucp=1,
            n="geoCard#",
            fpt=1,
            scale=1,
            et=2,
            rn=False,
            rsp=1,
            po=1,
        )[0]
        mc.setAttr(self.hairCard + ".inheritsTransform", 0)
        mc.xform((self.hairCard), t=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        self.pathCurve = str(mc.rename(mc.duplicate((self.pathInst), ilf=1, rr=1)[0], "pathCurve#"))
        pathInstShape = mc.ls((self.pathInst), s=1, dag=1, l=1)[0]
        self.nurbsTesselate = mc.listConnections(mc.ls((self.hairCard), s=1, dag=1, l=1), s=True, d=False)[0]
        self.extrude = mc.listConnections((self.nurbsTesselate), s=True, d=False)[0]
        mc.setAttr(self.extrude + ".useProfileNormal", 1)
        mc.connectAttr((self.pathCurve + ".translate"), (self.hairCard + ".translate"), f=1)
        mc.connectAttr((self.pathCurve + ".rotate"), (self.hairCard + ".rotate"), f=1)
        mc.connectAttr((self.pathCurve + ".scale"), (self.hairCard + ".scale"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleX"), (self.pathCurve + ".scaleY"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleX"), (self.pathCurve + ".scaleZ"), f=1)
        mc.connectAttr((self.pathCurve + ".rotatePivot"), (self.hairCard + ".rotatePivot"), f=1)
        mc.connectAttr((self.pathCurve + ".scalePivot"), (self.hairCard + ".scalePivot"), f=1)
        mc.connectAttr((self.pathCurve + ".rotatePivotTranslate"), (self.hairCard + ".rotatePivotTranslate"), f=1)
        mc.connectAttr((self.pathCurve + ".scalePivotTranslate"), (self.hairCard + ".scalePivotTranslate"), f=1)
        mc.connectAttr((pathInstShape + ".editPoints[0]"), (self.profileInst + ".translate"), f=1)
        mc.connectAttr((pathInstShape + ".editPoints[0]"), (self.extrude + ".pivot"), f=1)
        mc.xform((self.pathCurve), ws=1, t=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        mc.addAttr((self.pathCurve), ln="lengthDivisions", dv=(self.ldiv), smx=500, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="widthDivisions", dv=3, smx=11, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="Orientation", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Twist", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Width", dv=1, smx=5, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="Taper", dv=1, smx=5, at="double", min=0, k=1)
        mc.addAttr((self.pathCurve), ln="Profile", smn=(-2), dv=0, at="double", smx=2, k=1)
        mc.connectAttr((self.pathCurve + ".lengthDivisions"), (self.nurbsTesselate + ".vNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".widthDivisions"), (self.nurbsTesselate + ".uNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".Orientation"), (self.profileInst + ".rotateX"), f=1)
        mc.connectAttr((self.pathCurve + ".Twist"), (self.extrude + ".rotation"), f=1)
        mc.connectAttr((self.pathCurve + ".Width"), (self.profileInst + ".scaleZ"), f=1)
        mc.connectAttr((self.pathCurve + ".Taper"), (self.extrude + ".scale"), f=1)
        mc.connectAttr((self.pathCurve + ".Profile"), (self.profileInst + ".scaleY"), f=1)
        self.rebuild()
        self.polyNormal()
        self.UV()
        self.solidify()
        self.addMessage()
        self.hideNodes()
        self.curveThickness()
        self.addScaleFactor()
        self.group("curveCard#")
        return self.hairCardGrp + "|" + self.pathCurve

    def extrudeTube(self, pathInst, auto=True):
        self.pathInst = mc.filterExpand(pathInst, sm=9)[0]
        if not self.pathInst:
            print("Select nurbs or bezier curve")
            return 0
        spans = mc.getAttr(self.pathInst + ".spans")
        self.ldiv = 10
        self.refine = 50
        if auto:
            self.ldiv = 16 + spans * 3
            self.refine = 50 + spans * 3
        try:
            mc.disconnectAttr(self.pathInst + ".sx", self.pathInst + ".sy")
        except BaseException:
            pass

        try:
            mc.disconnectAttr(self.pathInst + ".sx", self.pathInst + ".sz")
        except BaseException:
            pass

        scaleFactor = self.sf
        if mc.attributeQuery("scaleFactor", n=(self.pathInst), ex=1):
            # CONTROLS["keepCurveAttributes"].isChecked()
            if True:
                scaleFactor = mc.getAttr(self.pathInst + ".scaleFactor")
        self.profileInst = mc.circle(
            c=(0, 0, 0),
            ch=1,
            d=3,
            ut=0,
            sw=360,
            n="profileCurve_inst#",
            s=8,
            r=(1 * scaleFactor),
            tol=0.01,
            nr=(
                0,
                1,
                0,
            ),
        )
        mc.makeIdentity((self.pathInst), n=0, s=1, r=1, t=1, apply=True, pn=1)
        curveOrigin = mc.pointPosition((self.pathInst + ".cv[0]"), w=1)
        mc.xform((self.pathInst), ws=1, piv=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        mc.move(0, 0, 0, (self.pathInst), rpr=1, ws=1)
        mc.makeIdentity((self.pathInst), n=0, s=1, r=1, t=1, apply=True, pn=1)
        mc.nurbsToPolygonsPref(polyType=1, vType=1, format=2, uType=1, vNumber=10, uNumber=3)
        self.hairCard = mc.extrude(
            (self.profileInst[0]),
            (self.pathInst),
            upn=1,
            ch=True,
            rotation=0,
            ucp=1,
            n="geoTube#",
            fpt=1,
            scale=1,
            et=2,
            rn=False,
            rsp=1,
            po=1,
        )[0]
        mc.setAttr(self.hairCard + ".inheritsTransform", 0)
        mc.xform((self.hairCard), t=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        self.pathCurve = str(mc.rename(mc.duplicate((self.pathInst), ilf=1, rr=1)[0], "pathCurve#"))
        pathInstShape = mc.ls((self.pathInst), s=1, dag=1, l=1)[0]
        self.nurbsTesselate = mc.listConnections((mc.ls((self.hairCard), s=1, dag=1, l=1)[0]), s=True, d=False)[0]
        self.extrude = mc.listConnections((self.nurbsTesselate), s=True, d=False)[0]
        mc.setAttr(self.extrude + ".useProfileNormal", 1)
        mc.connectAttr((self.pathCurve + ".translate"), (self.hairCard + ".translate"), f=1)
        mc.connectAttr((self.pathCurve + ".rotate"), (self.hairCard + ".rotate"), f=1)
        mc.connectAttr((self.pathCurve + ".scale"), (self.hairCard + ".scale"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleX"), (self.pathCurve + ".scaleY"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleX"), (self.pathCurve + ".scaleZ"), f=1)
        mc.connectAttr((self.pathCurve + ".rotatePivot"), (self.hairCard + ".rotatePivot"), f=1)
        mc.connectAttr((self.pathCurve + ".scalePivot"), (self.hairCard + ".scalePivot"), f=1)
        mc.connectAttr((self.pathCurve + ".rotatePivotTranslate"), (self.hairCard + ".rotatePivotTranslate"), f=1)
        mc.connectAttr((self.pathCurve + ".scalePivotTranslate"), (self.hairCard + ".scalePivotTranslate"), f=1)
        mc.connectAttr((pathInstShape + ".editPoints[0]"), (self.profileInst[0] + ".translate"), f=1)
        mc.connectAttr((pathInstShape + ".editPoints[0]"), (self.extrude + ".pivot"), f=1)
        mc.xform((self.pathCurve), ws=1, t=(curveOrigin[0], curveOrigin[1], curveOrigin[2]))
        mc.addAttr((self.pathCurve), ln="lengthDivisions", dv=(self.ldiv), smx=500, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="widthDivisions", dv=7, smx=53, at="long", min=4, k=1)
        mc.addAttr((self.pathCurve), ln="Orientation", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Twist", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="WidthX", dv=1, smx=20, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="WidthZ", dv=1, smx=20, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="Taper", dv=1, smx=5, at="double", min=0, k=1)
        mc.connectAttr((self.pathCurve + ".lengthDivisions"), (self.nurbsTesselate + ".vNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".widthDivisions"), (self.nurbsTesselate + ".uNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".Orientation"), (self.profileInst[0] + ".rotateY"), f=1)
        mc.connectAttr((self.pathCurve + ".Twist"), (self.extrude + ".rotation"), f=1)
        mc.connectAttr((self.pathCurve + ".WidthX"), (self.profileInst[0] + ".scaleX"), f=1)
        mc.connectAttr((self.pathCurve + ".WidthZ"), (self.profileInst[0] + ".scaleZ"), f=1)
        mc.connectAttr((self.pathCurve + ".Taper"), (self.extrude + ".scale"), f=1)
        self.rebuild()
        self.polyNormal()
        self.UV()
        self.solidify()
        self.addMessage()
        self.hideNodes()
        self.curveThickness()
        self.addScaleFactor()
        self.group("curveTube#")
        return self.hairCardGrp + "|" + self.pathCurve

    def warpCard(self, pathCurve, auto=True):
        self.pathCurve = mc.filterExpand(pathCurve, sm=9)
        if not self.pathCurve:
            print("Select at least one curve")
            return 0
        self.pathCurve = mc.rename(self.pathCurve, "pathCurve#")
        self.pathCurveShape = mc.ls((self.pathCurve), dag=1, s=1, l=1)[0]
        spans = mc.getAttr(self.pathCurveShape + ".spans")
        self.ldiv = 10
        self.refine = 20
        if auto:
            self.ldiv = 8 + spans * 2
            self.refine = 20 + spans
        try:
            mc.disconnectAttr(self.pathCurve + ".sx", self.pathCurve + ".sy")
        except BaseException:
            pass

        try:
            mc.disconnectAttr(self.pathCurve + ".sx", self.pathCurve + ".sz")
        except BaseException:
            pass

        scaleFactor = self.sf
        if mc.attributeQuery("scaleFactor", n=(self.pathCurve), ex=1):
            # CONTROLS["keepCurveAttributes"].isChecked()
            if True:
                scaleFactor = mc.getAttr(self.pathCurve + ".scaleFactor")
        self.pathInst = mc.curve(
            d=3,
            p=[
                (0, -0.0001, 0),
                (1.666667 * scaleFactor, 0, 0),
                (5 * scaleFactor, 0, 0),
                (
                    10 * scaleFactor,
                    0,
                    0,
                ),
                (15 * scaleFactor, 0, 0),
                (18.333333 * scaleFactor, 0, 0),
                (
                    20 * scaleFactor,
                    0,
                    0,
                ),
            ],
            k=[0, 0, 0, 1, 2, 3, 4, 4, 4],
            n="pathCurve_inst#",
        )
        mc.rebuildCurve((self.pathInst), s=30)
        self.profileInst = mc.curve(
            p=[
                (
                    0,
                    0,
                    -2.5 * scaleFactor,
                ),
                (0, 0.5 * scaleFactor, -1.666667 * scaleFactor),
                (0, 1 * scaleFactor, 0),
                (
                    0,
                    0.5 * scaleFactor,
                    1.666667 * scaleFactor,
                ),
                (0, 0, 2.5 * scaleFactor),
            ],
            k=[0, 0, 0, 1, 2, 2, 2],
            d=3,
            n="profileCurve_inst#",
        )
        mc.setAttr(self.profileInst + ".sy", 4)
        mc.nurbsToPolygonsPref(polyType=1, vType=1, format=2, uType=1, vNumber=10, uNumber=3)
        extrudeCmd = mc.extrude(
            (self.profileInst),
            (self.pathInst),
            upn=1,
            ch=True,
            rotation=0,
            ucp=1,
            n="geoCard#",
            fpt=1,
            scale=1,
            et=2,
            rn=False,
            rsp=1,
            po=1,
        )
        self.hairCard = extrudeCmd[0]
        self.extrude = extrudeCmd[1]
        self.nurbsTesselate = mc.listConnections((self.extrude), s=False, d=True, et=1, t="nurbsTessellate")[0]
        latticeDivisions = 10
        self.lattice = mc.lattice((self.hairCard), dv=[latticeDivisions, 2, 2], oc=True, ldv=[2, 2, 2], ol=1)
        mc.move(0, 0, 0, (self.lattice[1] + ".scalePivot"), (self.lattice[1] + ".rotatePivot"), a=1)
        points = []
        for i in range(latticeDivisions):
            points.append(mc.pointPosition((self.lattice[1] + ".pt[%s][1][0]" % i), l=1))

        self.twist = mc.nonLinear((self.hairCard), type="twist")
        mc.setAttr(self.twist[1] + ".rz", 90)
        mc.move(0, 0, 0, (self.twist), a=1, y=1)
        self.curveWarp = mc.deformer(ignoreSelected=1, type="curveWarp", n="curveWarp#")[0]
        mc.deformer((self.curveWarp), e=1, g=(self.hairCard))
        mc.connectAttr(self.pathCurveShape + ".worldSpace[0]", self.curveWarp + ".inputCurve")
        mc.setAttr(self.curveWarp + ".alignmentMode", 2)
        mc.setAttr(self.hairCard + ".inheritsTransform", 0)
        mc.setAttr(self.extrude + ".useProfileNormal", 1)
        mc.setAttr(self.curveWarp + ".samplingAccuracy", 0.333)
        mc.setAttr(self.curveWarp + ".twistRotation", 720)
        mc.addAttr((self.pathCurve), ln="lengthDivisions", dv=(self.ldiv), smx=500, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="widthDivisions", dv=3, smx=11, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="Orientation", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Twist", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="invTwist", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Width", dv=1, smx=5, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="LengthLock", at="enum", dv=0, en="Locked:Unlocked:", k=1)
        mc.addAttr((self.pathCurve), ln="Length", dv=1, smx=30, smn=(-30), at="double", k=1)
        mc.addAttr((self.pathCurve), ln="Offset", dv=0, smx=1, smn=(-1), at="double", min=(-30), max=30, k=1)
        mc.addAttr((self.pathCurve), ln="Taper", dv=1, smx=5, at="double", min=0, k=1)
        mc.addAttr((self.pathCurve), ln="Profile", smn=(-2), dv=0, at="double", smx=2, max=12, k=1)
        mc.addAttr((self.pathCurve), ln="profileSmoothing", dv=2, at="long", min=2, max=30, k=0)
        mc.addAttr((self.pathCurve), ln="profileMagnitude", dv=1, at="double", min=(-2), max=2, k=0)
        if not mc.attributeQuery("initialLatticePoints", n=(self.pathCurve), ex=1):
            mc.addAttr((self.pathCurve), ln="initialLatticePoints", dt="double3", m=1)
        for i in range(len(points)):
            (mc.setAttr)(self.pathCurve + ".initialLatticePoints[%s]" % i, *(points[i]), **{"type": "double3"})

        if not mc.attributeQuery("latticeMessage", n=(self.pathCurve), ex=1):
            mc.addAttr((self.pathCurve), ln="latticeMessage", at="message", k=0)
        mc.addAttr((self.lattice[1]), ln="latticeMessage", at="message", k=0)
        mc.connectAttr((self.pathCurve + ".lengthDivisions"), (self.nurbsTesselate + ".vNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".widthDivisions"), (self.nurbsTesselate + ".uNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".Twist"), (self.twist[0] + ".startAngle"), f=1)
        mc.connectAttr((self.pathCurve + ".invTwist"), (self.twist[0] + ".endAngle"), f=1)
        mc.connectAttr((self.pathCurve + ".Width"), (self.profileInst + ".scaleZ"), f=1)
        mc.connectAttr((self.pathCurve + ".Taper"), (self.extrude + ".scale"), f=1)
        mc.connectAttr((self.pathCurve + ".Profile"), (self.profileInst + ".scaleY"), f=1)
        mc.connectAttr((self.pathCurve + ".LengthLock"), (self.curveWarp + ".keepLength"), f=1)
        mc.connectAttr((self.pathCurve + ".Length"), (self.curveWarp + ".lengthScale"), f=1)
        mc.connectAttr((self.pathCurve + ".Offset"), (self.curveWarp + ".offset"), f=1)
        mc.connectAttr((self.pathCurve + ".latticeMessage"), (self.lattice[1] + ".latticeMessage"), f=1)
        mc.connectAttr((self.pathCurve + ".profileSmoothing"), (self.lattice[0] + ".localInfluenceS"), f=1)
        mc.connectAttr((self.pathCurve + ".profileMagnitude"), (self.lattice[0] + ".envelope"), f=1)
        twistHandle = mc.listRelatives((self.twist), c=1, pa=1)
        connectionCheck = mc.isConnected(self.twist[0] + ".startAngle", twistHandle[0] + ".startAngle")
        if not connectionCheck:
            mc.connectAttr((self.twist[0] + ".startAngle"), (twistHandle[0] + ".startAngle"), f=1)
            mc.connectAttr((self.twist[0] + ".endAngle"), (twistHandle[0] + ".endAngle"), f=1)
        mc.addAttr((self.pathCurve), ln="Magnitude", dv=0.5, at="double", h=1)
        ex = (
            "\n        {2}.rx = {1}.Orientation + {1}.rx;\n        {0}.rotation = 360 * (1 - {1}.Magnitude);\n       "
            " {0}.twistRotation = 720 * {1}.Magnitude;\n        ".format(
                self.curveWarp, self.pathCurve, self.lattice[1]
            )
        )
        self.magnExpr = mc.expression(ae=0, s=ex, n="twistOrienCalc#")
        self.rebuild(True)
        self.polyNormal()
        self.UV()
        self.solidify()
        self.addMessage()
        self.hideNodes()
        self.curveThickness()
        self.addScaleFactor()
        self.clearTweakNode()
        self.group("warpCard#")
        return self.hairCardGrp + "|" + self.pathCurve

    def warpTube(self, pathCurve, auto=True):
        self.pathCurve = mc.filterExpand(pathCurve, sm=9)[0]
        if not self.pathCurve:
            print("Select at least one curve")
            return 0
        self.pathCurve = mc.rename(self.pathCurve, "pathCurve#")
        self.pathCurveShape = mc.ls((self.pathCurve), dag=1, s=1, l=1)[0]
        spans = mc.getAttr(self.pathCurveShape + ".spans")
        self.ldiv = 10
        self.refine = 20
        if auto:
            self.ldiv = 16 + spans * 3
            self.refine = 20 + spans
        try:
            mc.disconnectAttr(self.pathCurve + ".sx", self.pathCurve + ".sy")
        except BaseException:
            pass

        try:
            mc.disconnectAttr(self.pathCurve + ".sx", self.pathCurve + ".sz")
        except BaseException:
            pass

        scaleFactor = self.sf
        if mc.attributeQuery("scaleFactor", n=(self.pathCurve), ex=1):
            # CONTROLS["keepCurveAttributes"].isChecked()
            if True:
                scaleFactor = mc.getAttr(self.pathCurve + ".scaleFactor")
        self.pathInst = mc.curve(
            d=3,
            p=[
                (0, -0.0001, 0),
                (1.666667 * scaleFactor, 0, 0),
                (5 * scaleFactor, 0, 0),
                (
                    10 * scaleFactor,
                    0,
                    0,
                ),
                (15 * scaleFactor, 0, 0),
                (18.333333 * scaleFactor, 0, 0),
                (
                    20 * scaleFactor,
                    0,
                    0,
                ),
            ],
            k=[0, 0, 0, 1, 2, 3, 4, 4, 4],
            n="pathCurve_inst#",
        )
        mc.rebuildCurve((self.pathInst), s=30)
        self.profileInst = mc.circle(
            c=(0, 0, 0),
            ch=1,
            d=3,
            ut=0,
            sw=360,
            n="profileCurve_inst#",
            s=8,
            r=(1 * scaleFactor),
            tol=0.01,
            nr=(
                0,
                1,
                0,
            ),
        )[0]
        mc.nurbsToPolygonsPref(polyType=1, vType=1, format=2, uType=1, vNumber=10, uNumber=7)
        extrudeCmd = mc.extrude(
            (self.profileInst),
            (self.pathInst),
            upn=1,
            ch=True,
            rotation=0,
            ucp=1,
            n="geoTube#",
            fpt=1,
            scale=1,
            et=2,
            rn=False,
            rsp=1,
            po=1,
        )
        self.hairCard = extrudeCmd[0]
        self.extrude = extrudeCmd[1]
        self.nurbsTesselate = mc.listConnections((self.extrude), s=False, d=True, et=1, t="nurbsTessellate")[0]
        self.curveWarp = mc.deformer(ignoreSelected=1, type="curveWarp", n="curveWarp#")[0]
        mc.deformer((self.curveWarp), e=1, g=(self.hairCard))
        mc.connectAttr(self.pathCurveShape + ".worldSpace[0]", self.curveWarp + ".inputCurve")
        mc.setAttr(self.hairCard + ".inheritsTransform", 0)
        mc.setAttr(self.extrude + ".useProfileNormal", 1)
        mc.setAttr(self.curveWarp + ".samplingAccuracy", 0.333)
        mc.setAttr(self.curveWarp + ".twistRotation", 720)
        mc.addAttr((self.pathCurve), ln="lengthDivisions", dv=(self.ldiv), smx=500, at="long", min=2, k=1)
        mc.addAttr((self.pathCurve), ln="widthDivisions", dv=7, smx=53, at="long", min=4, k=1)
        mc.addAttr((self.pathCurve), ln="Orientation", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="Twist", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="WidthX", dv=1, smx=5, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="WidthZ", dv=1, smx=5, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="LengthLock", at="enum", dv=0, en="Locked:Unlocked:", k=1)
        mc.addAttr((self.pathCurve), ln="Length", dv=1, smx=30, at="double", min=0.001, k=1)
        mc.addAttr((self.pathCurve), ln="Offset", dv=0, smx=1, smn=(-1), at="double", min=(-30), max=30, k=1)
        mc.addAttr((self.pathCurve), ln="Taper", dv=1, max=1.9, at="double", min=0, k=1)
        mc.connectAttr((self.pathCurve + ".lengthDivisions"), (self.nurbsTesselate + ".vNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".widthDivisions"), (self.nurbsTesselate + ".uNumber"), f=1)
        mc.connectAttr((self.pathCurve + ".Twist"), (self.extrude + ".rotation"), f=1)
        mc.connectAttr((self.pathCurve + ".Taper"), (self.extrude + ".scale"), f=1)
        mc.connectAttr((self.pathCurve + ".LengthLock"), (self.curveWarp + ".keepLength"), f=1)
        mc.connectAttr((self.pathCurve + ".Length"), (self.curveWarp + ".lengthScale"), f=1)
        mc.connectAttr((self.pathCurve + ".Offset"), (self.curveWarp + ".offset"), f=1)
        mc.addAttr((self.pathCurve), ln="Magnitude", dv=0.5, at="double", h=1)
        ex = (
            "\n        {2}.rx = {1}.Orientation + {1}.rx;\n        {0}.rotation = 360 * (1 - {1}.Magnitude);\n       "
            " {0}.twistRotation = 720 * {1}.Magnitude;\n        ".format(
                self.curveWarp, self.pathCurve, self.profileInst
            )
        )
        self.magnExpr = mc.expression(ae=0, s=ex, n="twistOrienCalc#")
        scaleEx = (
            "\n        if(({0}.WidthX >= 5 && {0}.WidthZ >= 5))\n        {{\n            {1}.scaleZ = 5;\n           "
            " {1}.scaleX = 5;\n            {2}.maxScale = abs({0}.WidthZ - 5) + abs({0}.WidthX - 5) + 2;\n        }}\n "
            "       else if (({0}.WidthX >= 5))\n        {{\n            {1}.scaleX = 5;\n            {1}.scaleZ ="
            " {0}.WidthZ;\n            {2}.maxScale = abs({0}.WidthX - 5) + 2;\n        }}\n        else if"
            " (({0}.WidthZ >= 5))\n        {{\n            {1}.scaleZ = 5;\n            {1}.scaleX = {0}.WidthX;\n     "
            "       {2}.maxScale = abs({0}.WidthZ - 5) + 2;\n        }}\n        else\n        {{\n           "
            " {2}.maxScale = 2;\n            {1}.scaleX = {0}.WidthX;\n            {1}.scaleZ = {0}.WidthZ;\n       "
            " }}\n        ".format(self.pathCurve, self.profileInst, self.curveWarp)
        )
        self.scaleExpr = mc.expression(ae=0, s=scaleEx, n="scaleManagement#")
        self.rebuild(True)
        self.polyNormal()
        self.UV()
        self.solidify()
        self.addMessage()
        self.hideNodes()
        self.curveThickness()
        self.addScaleFactor()
        self.clearTweakNode()
        self.group("warpTube#")
        return self.hairCardGrp + "|" + self.pathCurve

    def multiple(self, mode, hk=False):
        self.initialize()
        selPool = mc.filterExpand(mc.ls(sl=1, l=1, tr=1), sm=9)
        # try:
        #    selPool = utils.convertInstanceToObj(selPool)
        # except BaseException:
        #    pass
        if selPool == -1:
            return 0
        # CONTROLS["warpSwitch"].isChecked()
        if True or hk:
            mode = mode + 2
        create = "extrudeCards"
        if mode == 1:
            create = "extrudeTubes"
        else:
            if mode == 2:
                create = "warpCards"
            else:
                if mode == 3:
                    create = "warpTubes"
                else:
                    print("mode error")
        allCurves = list()
        if selPool:
            print("Creating " + create, len(selPool))
        else:
            print("Select at least one curve")
            return
        for pathInst in selPool:
            prevAttrs = attributes.getAttr(pathInst)
            graphAttrs = {}
            for attr in attributes.graphAttributes:
                if mc.attributeQuery(attr, n=pathInst, ex=1):
                    graphAttrs[attr] = mc.getAttr(pathInst + "." + attr)
            if utils.attrExists(pathInst, "lengthDivisions"):
                if mc.connectionInfo((pathInst + ".lengthDivisions"), isSource=1):
                    print("%s is not a compatible curve. Skipped." % pathInst)
                    continue
                attributes.deleteAttr(pathInst)
            if utils.attrExists(pathInst, "Axis"):
                if mc.connectionInfo((pathInst + ".Axis"), isSource=1):
                    print("%s is not a compatible curve. Skipped." % pathInst)
                    continue
                attributes.deleteAttr(pathInst)
            pathInst = mc.rename(pathInst, "pathCurve_inst#")
            returnCurve = list()
            pathInst = utils.checkIfBezier(pathInst)
            if mode == 0:
                returnCurve = self.extrudeCard(pathInst)
            elif mode == 1:
                returnCurve = self.extrudeTube(pathInst)
            else:
                if mode == 2:
                    returnCurve = self.warpCard(pathInst)
                else:
                    if mode == 3:
                        returnCurve = self.warpTube(pathInst)
            if returnCurve:
                # CONTROLS["keepCurveAttributes"].isChecked()
                if True:
                    for attr in graphAttrs:
                        values = graphAttrs[attr]
                        if attr == "profileCurve":
                            updateLattice(values, returnCurve)
                        else:
                            rebuildCurve = mc.listConnections(returnCurve + ".curveRefine")[0]
                            warp = mc.listConnections(rebuildCurve + ".outputCurve")[0]
                            graphValues = utils.fromStringToDouble2(values)
                            utils.setDouble2Attr(warp, attr, graphValues)
                    attributes.setAttr(returnCurve, prevAttrs)
                allCurves.append(returnCurve)
        mc.select(allCurves, r=1)

    def rebuild(self, output=None):
        inst = None
        if output:
            output = self.curveWarp + ".inputCurve"
            inst = self.pathCurve
        else:
            output = self.extrude + ".path"
            inst = self.pathInst
        self.rebuildCurve = mc.createNode("rebuildCurve")
        mc.setAttr(self.rebuildCurve + ".keepRange", 2)
        mc.addAttr((self.pathCurve), ln="curveRefine", smn=(-1), dv=(self.refine), at="long", smx=300, k=1)
        mc.addAttr((self.pathCurve), ln="curveSmooth", dv=0, smx=10, at="double", min=0, k=1)
        mc.connectAttr((inst + ".worldSpace[0]"), (self.rebuildCurve + ".inputCurve"), f=1)
        mc.connectAttr((self.rebuildCurve + ".outputCurve"), output, f=1)
        mc.connectAttr((self.pathCurve + ".curveRefine"), (self.rebuildCurve + ".spans"), f=1)
        mc.connectAttr((self.pathCurve + ".curveSmooth"), (self.rebuildCurve + ".smooth"), f=1)

    def polyNormal(self):
        self.polyNormalNode = mc.polyNormal((self.hairCard), ch=1, nm=2)[0]
        self.polySoftEdge = mc.polySoftEdge((self.hairCard), ch=1, a=180)[0]
        mc.addAttr((self.pathCurve), ln="reverseNormals", dv=1, en="reverse:off:", at="enum", k=1)
        mc.addAttr((self.pathCurve), ln="surfaceNormals", dv=180, smx=180, at="double", min=0, k=1)
        mc.connectAttr((self.pathCurve + ".reverseNormals"), (self.polyNormalNode + ".normalMode"), f=1)
        mc.connectAttr((self.pathCurve + ".surfaceNormals"), (self.polySoftEdge + ".angle"), f=1)

    def UV(self):
        self.polyFlipUV = mc.polyFlipUV((self.hairCard), ch=True, ft=1, up=True, pu=0.5, pv=0.5)[0]
        self.polyMoveUV_root = mc.polyMoveUV((self.hairCard), ch=True, pvu=0.5, pvv=0)[0]
        self.polyMoveUV_mid = mc.polyMoveUV((self.hairCard), ch=True, pvu=0.5, pvv=0.5)[0]
        mc.setAttr(self.polyFlipUV + ".usePivot", 1)
        mc.setAttr(self.polyFlipUV + ".pivotU", 0.5)
        mc.setAttr(self.polyFlipUV + ".pivotV", 0.5)
        mc.addAttr((self.pathCurve), ln="flipUV", dv=1, en="flip:off:", at="enum", k=1)
        mc.addAttr((self.pathCurve), ln="moveU", smn=(-1), dv=0, at="double", smx=1, k=1)
        mc.addAttr((self.pathCurve), ln="moveV", smn=(-1), dv=0, at="double", smx=1, k=1)
        mc.addAttr((self.pathCurve), ln="rotateUV", smn=(-360), dv=0, at="double", smx=360, k=1)
        mc.addAttr((self.pathCurve), ln="scaleU", smn=0, dv=1, at="double", smx=5, k=1)
        mc.addAttr((self.pathCurve), ln="scaleV", smn=0, dv=1, at="double", smx=5, k=1)
        uv_rotate = (
            "\n        {1}.pivotU = ({0}.translateU + 0.5);\n        {1}.pivotV = ({0}.translateV + 0.5);\n        "
            .format(self.polyMoveUV_root, self.polyMoveUV_mid)
        )
        self.UVexpr = mc.expression(ae=0, n="UV_Rotation#", s=uv_rotate)
        mc.connectAttr((self.pathCurve + ".flipUV"), (self.polyFlipUV + ".nodeState"), f=1)
        mc.connectAttr((self.pathCurve + ".moveU"), (self.polyMoveUV_root + ".translateU"), f=1)
        mc.connectAttr((self.pathCurve + ".moveV"), (self.polyMoveUV_root + ".translateV"), f=1)
        mc.connectAttr((self.pathCurve + ".rotateUV"), (self.polyMoveUV_mid + ".rotationAngle"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleU"), (self.polyMoveUV_root + ".scaleU"), f=1)
        mc.connectAttr((self.pathCurve + ".scaleV"), (self.polyMoveUV_root + ".scaleV"), f=1)

    def solidify(self):
        self.solidifyNode = mc.createNode("polyExtrudeFace")
        self.solidifyChoice = mc.createNode("choice")
        mc.addAttr((self.pathCurve), ln="solidify", dv=0, at="bool", k=1)
        mc.addAttr((self.pathCurve), ln="solidifyThickness", smn=0, dv=0.25, at="double", smx=10, k=1)
        mc.addAttr((self.pathCurve), ln="solidifyDivisions", smn=0, dv=0, at="long", smx=10, k=1)
        mc.addAttr((self.pathCurve), ln="solidifyScaleX", smn=(-10), dv=1, at="double", smx=10, k=1)
        mc.addAttr((self.pathCurve), ln="solidifyScaleY", smn=(-10), dv=1, at="double", smx=10, k=1)
        mc.addAttr((self.pathCurve), ln="solidifyOffset", smn=(-10), dv=0, at="double", smx=10, k=1)
        mc.addAttr((self.pathCurve), ln="solidifyNormals", smn=0, dv=30, at="double", smx=180, k=1)
        mc.setAttr((self.solidifyNode + ".inputComponents"), 1, "f[*]", type="componentList")
        mc.connectAttr((self.hairCard + ".worldMatrix[0]"), (self.solidifyNode + ".manipMatrix"), f=1)
        mc.connectAttr((self.polyMoveUV_mid + ".output"), (self.solidifyChoice + ".input[0]"), f=1)
        mc.connectAttr((self.polyMoveUV_mid + ".output"), (self.solidifyNode + ".inputPolymesh"), f=1)
        mc.connectAttr((self.solidifyNode + ".output"), (self.solidifyChoice + ".input[1]"), f=1)
        mc.connectAttr((self.solidifyChoice + ".output"), (self.hairCard + ".inMesh"), f=1)
        mc.connectAttr((self.pathCurve + ".solidify"), (self.solidifyChoice + ".selector"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyThickness"), (self.solidifyNode + ".localTranslateZ"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyDivisions"), (self.solidifyNode + ".divisions"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyScaleX"), (self.solidifyNode + ".localScaleX"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyScaleY"), (self.solidifyNode + ".localScaleY"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyOffset"), (self.solidifyNode + ".offset"), f=1)
        mc.connectAttr((self.pathCurve + ".solidifyNormals"), (self.solidifyNode + ".smoothingAngle"), f=1)

    def addMessage(self):
        if not mc.attributeQuery("gsmessage", n=(self.pathCurve), ex=1):
            mc.addAttr((self.pathCurve), ln="gsmessage", at="message", k=0)

    def hideNodes(self):
        nodes = [
            self.nurbsTesselate,
            self.curveWarp,
            self.polyFlipUV,
            self.polyMoveUV_mid,
            self.polyMoveUV_tip,
            self.polyMoveUV_root,
            self.solidifyNode,
            self.solidifyChoice,
            self.extrude,
            self.magnExpr,
            self.scaleExpr,
            self.UVexpr,
            self.rebuildCurve,
            self.polyNormalNode,
            self.polySoftEdge,
            self.lattice,
            self.twist,
        ]
        assetList = []
        for node in nodes:
            if node:
                if isinstance(node, list):
                    for n in node:
                        if mc.objExists(n):
                            assetList.append(n)
                            mc.setAttr(n + ".isHistoricallyInteresting", 0)

                elif mc.objExists(node):
                    assetList.append(node)
                    mc.setAttr(node + ".isHistoricallyInteresting", 0)

    def curveThickness(self):
        shape = mc.ls((self.pathCurve), dag=1, s=1, l=1)[0]
        mc.setAttr(shape + ".lineWidth", self.globalThickness)

    def addScaleFactor(self):
        # CONTROLS["keepCurveAttributes"].isChecked()
        if not True:
            if mc.attributeQuery("scaleFactor", n=(self.pathCurve), ex=1):
                mc.deleteAttr(self.pathCurve + ".scaleFactor")
        if not mc.attributeQuery("scaleFactor", n=(self.pathCurve), ex=1):
            mc.addAttr((self.pathCurve), ln="scaleFactor", at="double", dv=(self.sf))
            mc.setAttr(self.pathCurve + ".scaleFactor", self.sf)

    def resetProfileCurve():
        # currentProfile = CONTROLS["profileCurve"].getGraph()
        currentProfile = False
        if currentProfile:
            graphValues = utils.fromStringToDouble2(currentProfile)
            for i in range(len(graphValues)):
                graphValues[i][1] = 0.5

            newString = utils.fromDouble2ToString(graphValues)
            # CONTROLS["profileCurve"].setGraph(newString)
            updateLattice(newString)

    def setProfileCurve(profileCurve):
        if profileCurve:
            newString = utils.fromDouble2ToString(profileCurve)
            # CONTROLS["profileCurve"].setGraph(newString)
            updateLattice(newString)

    def clearTweakNode(self):
        pass

    def group(self, grpName, fake=False):
        constructionGrp = None
        if fake:
            constructionGrp = mc.createNode("transform", n="instances#")
            self.hairCard = self.geo
        else:
            if self.lattice:
                constructionGrp = mc.group(
                    (self.pathInst), (self.profileInst), (self.lattice), (self.twist), n="instances#"
                )
            else:
                constructionGrp = mc.group((self.pathInst), (self.profileInst), n="instances#")
        mc.setAttr(constructionGrp + ".inheritsTransform", 0)
        mc.setAttr((constructionGrp + ".tx"), lock=True)
        mc.setAttr((constructionGrp + ".ty"), lock=True)
        mc.setAttr((constructionGrp + ".tz"), lock=True)
        mc.setAttr((constructionGrp + ".rx"), lock=True)
        mc.setAttr((constructionGrp + ".ry"), lock=True)
        mc.setAttr((constructionGrp + ".rz"), lock=True)
        mc.setAttr((constructionGrp + ".sx"), lock=True)
        mc.setAttr((constructionGrp + ".sy"), lock=True)
        mc.setAttr((constructionGrp + ".sz"), lock=True)
        hairGrp = self.currentLayerInd()
        if mc.objExists("curveGrp_%s_Inst" % hairGrp) != 1:
            mc.createDisplayLayer(constructionGrp, nr=1, n="curveGrp_%s_Inst" % hairGrp)
            mc.setAttr("curveGrp_%s_Inst" % hairGrp + ".displayType", 2)
            mc.setAttr("curveGrp_%s_Inst" % hairGrp + ".visibility", 0)
        else:
            mc.editDisplayLayerMembers(("curveGrp_%s_Inst" % hairGrp), constructionGrp, nr=1)
            mc.setAttr("curveGrp_%s_Inst" % hairGrp + ".displayType", 2)
            mc.setAttr("curveGrp_%s_Inst" % hairGrp + ".visibility", 0)
        if mc.objExists("curveGrp_%s_Geo" % hairGrp) != 1:
            mc.createDisplayLayer((self.hairCard), nr=1, n="curveGrp_%s_Geo" % hairGrp)
            mc.setAttr("curveGrp_%s_Geo" % hairGrp + ".displayType", 2)
        else:
            mc.editDisplayLayerMembers(("curveGrp_%s_Geo" % hairGrp), (self.hairCard), nr=1)
        if mc.objExists("curveGrp_%s_Curve" % hairGrp) != 1:
            mc.createDisplayLayer((self.pathCurve), nr=1, n="curveGrp_%s_Curve" % hairGrp)
        else:
            mc.editDisplayLayerMembers(("curveGrp_%s_Curve" % hairGrp), (self.pathCurve), nr=1)
        self.hairCardGrp = str(mc.group((self.pathCurve), constructionGrp, (self.hairCard), n=grpName))
        mc.setAttr(self.hairCardGrp + "|" + self.hairCard + ".inheritsTransform", 1)
        mc.setAttr((self.hairCardGrp + ".tx"), lock=True)
        mc.setAttr((self.hairCardGrp + ".ty"), lock=True)
        mc.setAttr((self.hairCardGrp + ".tz"), lock=True)
        mc.setAttr((self.hairCardGrp + ".rx"), lock=True)
        mc.setAttr((self.hairCardGrp + ".ry"), lock=True)
        mc.setAttr((self.hairCardGrp + ".rz"), lock=True)
        mc.setAttr((self.hairCardGrp + ".sx"), lock=True)
        mc.setAttr((self.hairCardGrp + ".sy"), lock=True)
        mc.setAttr((self.hairCardGrp + ".sz"), lock=True)


def show_window():
    if mc.window("autoHairWin", exists=True):
        mc.deleteUI("autoHairWin")
    win = mc.window("autoHairWin", title="自动化头发面片处理", widthHeight=(300, 100))
    mc.columnLayout(adjustableColumn=True)
    mc.text(label="1. 选中一个或多个头发面片对象\n2. 点击按钮自动处理")
    mc.button(label="创建新的", command=create_new)
    mc.button(label="从已选中曲线创建card", command=create_from_selected)
    mc.button(label="从已选中曲线创建tube", command=create_tube_from_selected)
    mc.button(label="归零测试", command=zero_tube_from_selected)
    mc.setParent("..")
    mc.showWindow(win)


def create_new(*args):
    Create().new(0)


def create_from_selected(*args):
    Create().multiple(0)


def create_tube_from_selected(*args):
    Create().multiple(1)


def zero_tube_from_selected(*args):
    utils.resetSingleGraph("twist")


def updateLattice(values, customTarget=None):
    sel = mc.filterExpand(mc.ls(sl=1, tr=1), sm=9)
    if not sel:
        sel = mc.filterExpand(mc.ls(hl=1, o=1), sm=9)
    if not sel and not customTarget:
        return None
    sel = customTarget if customTarget else sel[-1]
    if mc.attributeQuery("latticeMessage", n=sel, ex=1):
        lattice = mc.listConnections(sel + ".latticeMessage")
    else:
        return None
    lattice = lattice[0]
    graphValues = utils.fromStringToDouble2(values)
    latticeDiv = mc.getAttr(lattice + ".sDivisions")
    if len(graphValues) != latticeDiv:
        ffd = mc.listConnections(lattice + ".latticeOutput")
        mc.lattice(ffd, e=1, rt=1)
        mc.lattice(ffd, e=1, dv=[len(graphValues), 2, 2])
        latticeDiv = mc.getAttr(lattice + ".sDivisions")
    currentPoints = []
    initialPoints = []
    for i in range(latticeDiv):
        currentPoints.append(mc.pointPosition((lattice + ".pt[%s][1][0]" % i), l=1))

    for i in range(mc.getAttr((sel + ".initialLatticePoints"), s=1)):
        initialPoints.append(mc.getAttr(sel + ".initialLatticePoints[%s]" % i)[0])

    newPoints = []
    for i in range(latticeDiv):
        x = mt.lerp(graphValues[i][0], 0.5, -0.5, 1, 0)
        y = mt.lerp(graphValues[i][1], 1.5, -0.5, 1, 0)
        newPoints.append((x, y))

    for i in range(len(newPoints)):
        mc.move((newPoints[i][0]), (newPoints[i][1]), (lattice + ".pt[%s][1][0]" % i), xy=1, ls=1)
        mc.move((newPoints[i][0]), (newPoints[i][1]), (lattice + ".pt[%s][1][1]" % i), xy=1, ls=1)
        mc.move((newPoints[i][0]), (newPoints[i][1]), (lattice + ".pt[%s][0][0]" % i), x=1, ls=1)
        mc.move((newPoints[i][0]), (newPoints[i][1]), (lattice + ".pt[%s][0][1]" % i), x=1, ls=1)


# show_window()
