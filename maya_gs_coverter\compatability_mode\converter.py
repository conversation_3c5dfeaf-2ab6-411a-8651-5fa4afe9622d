# Import third-party modules
import logging

import maya.api.OpenMaya as om
import maya.cmds as cmds
import numpy as np

# Import local modules
import maya_gs_coverter.compatability_mode.compatibility_utils as compatibility_utils
import maya_gs_coverter.hair as hair
import maya_gs_coverter.utils.utils as hair_utils
from maya_gs_coverter.uv_grid_cache import UVGridCache

from dataclasses import dataclass
from typing import List, Tuple, Optional


ROOT_RATIO = 0.2

Vec3 = Tuple[float, float, float]

@dataclass
class SectionSample:
    mid: Vec3
    left: Vec3
    right: Vec3
    width: float
    height: float
    normal: Optional[Vec3]

@dataclass
class SourceCardData:
    mesh_name: str
    curve_name: str
    degree: int
    samples: List[SectionSample]
    u_min: float
    u_max: float
    v_min: float
    v_max: float

@dataclass
class GSCard:
    curve_name: str
    mesh_name: str
    num_segments: int
    uv_grid: Optional[list] = None


def build_uv_face_grid_index(mesh_fn, uv_set, u_min, u_max, v_min, v_max, grid_resolution=32):
    """
    为 mesh 的 UV 面包围盒建立均匀网格索引，以减少 getPointAtUV 的面遍历。

    Args:
        mesh_fn (om.MFnMesh): 目标网格的函数集。
        uv_set (str): UV 集名称。
        u_min (float): UV U 方向的最小值。
        u_max (float): UV U 方向的最大值。
        v_min (float): UV V 方向的最小值。
        v_max (float): UV V 方向的最大值。
        grid_resolution (int, optional): 网格分辨率。默认 32。

    Returns:
        dict: 包含以下键：
            - grid (dict[tuple[int, int], list[tuple[int, float, float, float, float]]]):
              每个网格单元对应的候选面记录 (fid, umin, umax, vmin, vmax)。
            - grid_resolution (int): 网格分辨率。
            - u_min (float): U 最小值。
            - u_max (float): U 最大值。
            - v_min (float): V 最小值。
            - v_max (float): V 最大值。
            - u_range (float): U 范围。
            - v_range (float): V 范围。
    """
    u_range = max(u_max - u_min, 1e-8)
    v_range = max(v_max - v_min, 1e-8)
    grid = {}
    face_count = mesh_fn.numPolygons

    # 预取全局UV数组与面-UV分配
    try:
        u_all, v_all = mesh_fn.getUVs(uvSet=uv_set)
    except Exception:
        # 兜底：尝试当前UV集或默认map1
        try:
            alt = mesh_fn.currentUVSetName()
            u_all, v_all = mesh_fn.getUVs(uvSet=alt)
        except Exception:
            u_all, v_all = mesh_fn.getUVs()
    try:
        counts, indices = mesh_fn.getAssignedUVs(uv_set)
    except Exception:
        try:
            counts, indices = mesh_fn.getAssignedUVs(mesh_fn.currentUVSetName())
        except Exception:
            counts, indices = mesh_fn.getAssignedUVs()

    # 为每个面计算在 indices 中的起止偏移，便于快速切片
    face_offsets = [0] * (face_count + 1)
    count_len = len(counts)
    for f in range(face_count):
        face_offsets[f + 1] = face_offsets[f] + (counts[f] if f < count_len else 0)

    for fid in range(face_count):
        start = face_offsets[fid]
        end = face_offsets[fid + 1]
        if end <= start:
            continue
        uv_ids = indices[start:end]
        if not uv_ids:
            continue
        try:
            fu = [u_all[k] for k in uv_ids]
            fv = [v_all[k] for k in uv_ids]
        except Exception:
            continue
        if not fu or not fv:
            continue
        fumin, fumax = min(fu), max(fu)
        fvmin, fvmax = min(fv), max(fv)

        # 映射到网格坐标，并做裁剪与夹取
        i0 = int((max(fumin, u_min) - u_min) / u_range * grid_resolution)
        i1 = int((min(fumax, u_max) - u_min) / u_range * grid_resolution)
        j0 = int((max(fvmin, v_min) - v_min) / v_range * grid_resolution)
        j1 = int((min(fvmax, v_max) - v_min) / v_range * grid_resolution)

        i0 = max(0, min(grid_resolution - 1, i0))
        i1 = max(0, min(grid_resolution - 1, i1))
        j0 = max(0, min(grid_resolution - 1, j0))
        j1 = max(0, min(grid_resolution - 1, j1))

        for i in range(i0, i1 + 1):
            for j in range(j0, j1 + 1):
                key = (i, j)
                if key not in grid:
                    grid[key] = []
                grid[key].append((fid, fumin, fumax, fvmin, fvmax))

    return {
        "grid": grid,
        "grid_resolution": grid_resolution,
        "u_min": u_min,
        "u_max": u_max,
        "v_min": v_min,
        "v_max": v_max,
        "u_range": u_range,
        "v_range": v_range,
    }


def _cell_index_from_uv(index_info, u, v):
    gr = index_info["grid_resolution"]
    u_min, u_range = index_info["u_min"], index_info["u_range"]
    v_min, v_range = index_info["v_min"], index_info["v_range"]
    i = int((u - u_min) / u_range * gr)
    j = int((v - v_min) / v_range * gr)
    i = max(0, min(gr - 1, i))
    j = max(0, min(gr - 1, j))
    return i, j


def find_point_at_uv_with_index(mesh_fn, uv_set, u, v, tolerance, index_info, max_expand=3):
    """
    利用预构建的 UV 面网格索引在 (u, v) 附近优先尝试相邻单元内的面，命中则返回世界坐标；
    若在指定扩展层数内未命中，则回退到全量面遍历。

    Args:
        mesh_fn (om.MFnMesh): 网格函数集。
        uv_set (str): UV 集名称。
        u (float): 采样 U 值。
        v (float): 采样 V 值。
        tolerance (float): getPointAtUV 的容差。
        index_info (dict): 由 build_uv_face_grid_index 返回的索引信息。
        max_expand (int, optional): 向外扩展 ring 的层数。默认 3。

    Returns:
        tuple[float, float, float] | None: 命中的世界坐标 (x, y, z)，未命中返回 None。
    """
    grid = index_info["grid"]
    gr = index_info["grid_resolution"]
    i, j = _cell_index_from_uv(index_info, u, v)

    tried = set()

    def try_faces(face_records):
        for rec in face_records:
            fid, fumin, fumax, fvmin, fvmax = rec
            if fid in tried:
                continue
            tried.add(fid)
            # 先用面UV包围盒做快速排除
            if u < fumin - 1e-8 or u > fumax + 1e-8 or v < fvmin - 1e-8 or v > fvmax + 1e-8:
                continue
            try:
                p = mesh_fn.getPointAtUV(fid, u, v, om.MSpace.kWorld, uv_set, tolerance)
                return (p.x, p.y, p.z)
            except Exception:
                continue
        return None

    # 先尝试当前cell
    faces = grid.get((i, j), [])
    pt = try_faces(faces)
    if pt is not None:
        return pt

    # 扩展到周围ring（稀疏到稠密）
    for r in range(1, max_expand + 1):
        agg = []
        i_min, i_max = max(0, i - r), min(gr - 1, i + r)
        j_min, j_max = max(0, j - r), min(gr - 1, j + r)
        # 上下边
        for ii in range(i_min, i_max + 1):
            agg.extend(grid.get((ii, j_min), []))
            if j_max != j_min:
                agg.extend(grid.get((ii, j_max), []))
        # 左右边（去除四角重复）
        for jj in range(j_min + 1, j_max):
            agg.extend(grid.get((i_min, jj), []))
            if i_max != i_min:
                agg.extend(grid.get((i_max, jj), []))
        pt = try_faces(agg)
        if pt is not None:
            return pt

    # 最后回退：全量遍历剩余面
    face_count = mesh_fn.numPolygons
    for fid in range(face_count):
        if fid in tried:
            continue
        try:
            p = mesh_fn.getPointAtUV(fid, u, v, om.MSpace.kWorld, uv_set, tolerance)
            return (p.x, p.y, p.z)
        except Exception:
            continue
    return None


def find_row_left_right_points(mesh_fn, uv_set, v, tolerance, index_info, neighbor_rows=1):
    """
    在给定的 V 行，查找左右两端的 UV 交点，并返回对应的 3D 点坐标。

    此函数使用 UV 网格索引仅遍历该行附近的候选面，并基于 getUVs 与 getAssignedUVs
    重构每个面的 UV 顶点序列。

    Args:
        mesh_fn (om.MFnMesh): 网格函数集。
        uv_set (str): UV 集名称。
        v (float): 指定的 V 行。
        tolerance (float): getPointAtUV 的容差。
        index_info (dict): build_uv_face_grid_index 返回的索引信息。
        neighbor_rows (int, optional): 垂直方向考虑的相邻行数。默认 1。

    Returns:
        tuple[tuple[float, float, float] | None, tuple[float, float, float] | None]:
        (left_pt, right_pt)。若未命中对应端点则为 None。
    """
    grid = index_info["grid"]
    gr = index_info["grid_resolution"]
    u_min_g, u_max_g = index_info["u_min"], index_info["u_max"]
    v_min_g, v_max_g = index_info["v_min"], index_info["v_max"]

    # 避开边界
    epsV = max(1e-6, index_info["v_range"] * 1e-5)
    v_safe = min(max(v, v_min_g + epsV), v_max_g - epsV)

    if v_safe < v_min_g - 1e-8 or v_safe > v_max_g + 1e-8:
        return (None, None)

    # 收集该V行附近的候选面ID
    _, j = _cell_index_from_uv(index_info, (u_min_g + u_max_g) * 0.5, v_safe)
    fids = set()
    for dj in range(-neighbor_rows, neighbor_rows + 1):
        jj = j + dj
        if jj < 0 or jj >= gr:
            continue
        for i in range(gr):
            for rec in grid.get((i, jj), []):
                fids.add(rec[0])

    # 兜底：如果索引该行为空，用索引中所有面的集合；再不行用全量面
    if not fids:
        for recs in grid.values():
            for fid, *_ in recs:
                fids.add(fid)
        if not fids:
            fids = set(range(mesh_fn.numPolygons))

    # 预取全局UV与分配表
    try:
        u_all, v_all = mesh_fn.getUVs(uvSet=uv_set)
    except Exception:
        try:
            alt = mesh_fn.currentUVSetName()
            u_all, v_all = mesh_fn.getUVs(uvSet=alt)
        except Exception:
            u_all, v_all = mesh_fn.getUVs()
    try:
        counts, indices = mesh_fn.getAssignedUVs(uv_set)
    except Exception:
        try:
            counts, indices = mesh_fn.getAssignedUVs(mesh_fn.currentUVSetName())
        except Exception:
            counts, indices = mesh_fn.getAssignedUVs()

    # 面起止偏移
    face_count = mesh_fn.numPolygons
    face_offsets = [0] * (face_count + 1)
    for f in range(face_count):
        face_offsets[f + 1] = face_offsets[f] + (counts[f] if f < len(counts) else 0)

    # 逐面计算与水平线 v 的相交U值
    face_hits = {}  # fid -> sorted [u_cross...]
    for fid in fids:
        start = face_offsets[fid]
        end = face_offsets[fid + 1]
        if end <= start:
            continue
        uv_ids = indices[start:end]
        if not uv_ids:
            continue
        fu = [u_all[k] for k in uv_ids]
        fv = [v_all[k] for k in uv_ids]
        if not fu or not fv:
            continue
        # 快速排除
        fumin, fumax = min(fu), max(fu)
        fvmin, fvmax = min(fv), max(fv)
        if v_safe < fvmin - 1e-8 or v_safe > fvmax + 1e-8:
            continue
        n = len(fu)
        hits = []
        for k in range(n):
            u0, v0 = fu[k], fv[k]
            u1, v1 = fu[(k + 1) % n], fv[(k + 1) % n]
            if (v0 <= v_safe < v1) or (v1 <= v_safe < v0):
                if v1 != v0:
                    t = (v_safe - v0) / float(v1 - v0)
                    uc = u0 + t * (u1 - u0)
                    hits.append(uc)
        if len(hits) >= 2:
            hits.sort()
            face_hits[fid] = hits

    if not face_hits:
        return (None, None)

    # 计算全局左右端（取所有面相交U的最小和最大）
    left_fid, left_u = None, None
    right_fid, right_u = None, None
    for fid, hits in face_hits.items():
        if hits:
            u_l = hits[0]
            u_r = hits[-1]
            if left_u is None or u_l < left_u:
                left_u, left_fid = u_l, fid
            if right_u is None or u_r > right_u:
                right_u, right_fid = u_r, fid

    # 将左右端点略微内移以避免恰在边界导致的命不中
    def nudge_inside(fid, target_u, is_left):
        hits = face_hits.get(fid, [])
        if len(hits) < 2:
            return target_u
        eps = max(1e-6, index_info["u_range"] * 1e-6)
        for a, b in zip(hits[::2], hits[1::2]):
            if a - 1e-8 <= target_u <= b + 1e-8:
                return a + eps if is_left else b - eps
        a, b = hits[0], hits[1] if len(hits) > 1 else (hits[0], hits[0])
        return a + eps if is_left else b - eps

    left_pt = None
    right_pt = None
    if left_fid is not None:
        u_try = nudge_inside(left_fid, left_u, True)
        try:
            p = mesh_fn.getPointAtUV(left_fid, u_try, v_safe, om.MSpace.kWorld, uv_set, tolerance)
            left_pt = (p.x, p.y, p.z)
        except Exception:
            left_pt = None
    if right_fid is not None:
        u_try = nudge_inside(right_fid, right_u, False)
        try:
            p = mesh_fn.getPointAtUV(right_fid, u_try, v_safe, om.MSpace.kWorld, uv_set, tolerance)
            right_pt = (p.x, p.y, p.z)
        except Exception:
            right_pt = None

    return (left_pt, right_pt)


def find_point_and_section_metrics_at_uv(mesh_fn, uv_set, u, v, tolerance, index_info, neighbor_rows=1):
    """
    在指定 (u, v) 处综合查询中心点与截面度量。

    该函数首先定位 (u, v) 命中的 3D 中心点，然后在同一 V 行上查找左右端点，
    计算宽度（左右端点距离）、高度（中心到端点连线的最短距离）以及法线方向。

    Args:
        mesh_fn (om.MFnMesh): 网格函数集。
        uv_set (str): UV 集名称。
        u (float): 采样 U 值。
        v (float): 采样 V 值。
        tolerance (float): getPointAtUV 的容差。
        index_info (dict): build_uv_face_grid_index 的返回值。
        neighbor_rows (int, optional): V 行相邻行扩展数量。默认 1。

    Returns:
        tuple:
            center (tuple[float, float, float] | None): (x, y, z) 中心点坐标；未命中为 None。
            left_pt (tuple[float, float, float] | None): 左端点坐标；未命中为 None。
            right_pt (tuple[float, float, float] | None): 右端点坐标；未命中为 None。
            width (float): 左右端点间距离；未命中时为 0.0。
            height (float): 中心到端点直线的最短距离；未命中或退化时为 0.0。
            normal (tuple[float, float, float] | None): 该点法线方向，未命中为 None。
    """
    left_pt = None
    right_pt = None
    width = 0.0
    height = 0.0
    normal = None
    center = find_point_at_uv_with_index(mesh_fn, uv_set, u, v, tolerance, index_info)
    if center is None:
        center = compatibility_utils.find_point_at_uv_on_any_face(mesh_fn, uv_set, u, v, tolerance)
    if center:
        left_pt, right_pt = find_row_left_right_points(
            mesh_fn, uv_set, v, tolerance, index_info, neighbor_rows=neighbor_rows
        )
        # 宽度：左右两点的距离
        width = np.linalg.norm(np.array(right_pt) - np.array(left_pt))
        # 高度：center 到 (left_pt, right_pt) 直线的最短距离
        a = np.array(left_pt)
        b = np.array(right_pt)
        c = np.array(center)
        ab = b - a
        ac = c - a
        ab_len = np.linalg.norm(ab)
        if ab_len > 1e-8:
            height = np.linalg.norm(np.cross(ab, ac)) / ab_len
            height = 0.0 if height < 0.1 else height
        else:
            height = 0.0
        # 法线：优先使用 getClosestNormal，更稳健；失败则回退 getClosestPointAndNormal
        raw_normal = None
        try:
            raw_normal, _ = mesh_fn.getClosestNormal(om.MPoint(*center), om.MSpace.kWorld)
        except Exception as e:
            cmds.warning(f"getClosestNormal failed: {e}")
            try:
                # 某些版本返回 (pt, normal, faceId, triId)；使用可变解包
                ret = mesh_fn.getClosestPointAndNormal(om.MPoint(*center), om.MSpace.kWorld)
                raw_normal = ret[1] if isinstance(ret, (list, tuple)) and len(ret) >= 2 else None
            except Exception as e2:
                cmds.warning(f"getClosestPointAndNormal failed: {e2}")
                raw_normal = None
        if raw_normal is not None:
            ln = (raw_normal.x**2 + raw_normal.y**2 + raw_normal.z**2) ** 0.5
            normal = (raw_normal.x / ln, raw_normal.y / ln, raw_normal.z / ln) if ln > 1e-8 else (0.0, 0.0, 0.0)
        else:
            normal = None
    return center, left_pt, right_pt, width, height, normal


def create_centerline_curve_from_mesh(mesh_transform, curve_degree=3, tolerance=1e-3) -> SourceCardData:
    """
    从选中的Mesh（假设为发片）的UV中心线创建一根NURBS曲线，并返回语义化的数据结构。

    Args:
        mesh_transform (str): Mesh 的 transform 名称。
        curve_degree (int): 生成曲线的阶数（1=线性, 3=平滑）。
        tolerance (float): getPointAtUV 的容差，默认 1e-3。

    Returns:
        SourceCardData: 包含曲线名、阶数以及每个采样段的几何数据。
    """
    # 1. 获取选中的Mesh
    if not mesh_transform:
        cmds.warning("请先选择一个Mesh的Transform节点。")
        return
    mesh_shape = compatibility_utils.get_mesh_shape_from_transform(mesh_transform)
    if not mesh_shape:
        cmds.warning("选择的对象没有找到Mesh Shape。")
        return

    # 使用OpenMaya API获取更精确的信息
    mesh_fn = compatibility_utils.build_mesh_fn(mesh_shape)

    try:
        # 2. 确定UV中心线
        # 读取UV数据并计算边界（替代 getUVBoundary）
        uv_set = compatibility_utils.get_current_uv_set_name(mesh_fn)
        u_arr, v_arr = compatibility_utils.get_uv_arrays(mesh_fn, uv_set)
        if len(u_arr) == 0 or len(v_arr) == 0 or len(u_arr) != len(v_arr):
            cmds.warning("无法读取到有效的UV数据。")
            return

        u_min, u_max = min(u_arr), max(u_arr)
        v_min, v_max = min(v_arr), max(v_arr)

        # 中心U值
        u_center = (u_min + u_max) / 2.0

        # 3. 在UV中心线上采样并查询3D位置
        points_3d = []
        left_pts = []
        right_pts = []
        widths = []
        heights = []
        normals = []
        v_range = v_max - v_min

        if v_range < 1e-2:
            cmds.warning("UV的V向范围太小，无法创建曲线。")
            return

        # 自动根据面片顶点数确定采样点数量
        num_points = compatibility_utils.compute_num_points_from_mesh(mesh_fn)

        # 首尾点：使用模型UV中最高点和最低点（规则封装函数）
        top_uv = compatibility_utils.select_top_uv(u_arr, v_arr, threshold=0.01)
        bottom_uv = compatibility_utils.select_bottom_uv(u_arr, v_arr)

        # 构建面UV包围盒网格索引
        index_info = build_uv_face_grid_index(mesh_fn, uv_set, u_min, u_max, v_min, v_max, grid_resolution=32)

        for i in range(num_points):
            # 4. 查询3D位置 (核心步骤)
            # 使用 getPointAtUV 在世界空间中查找点
            # 参数: u, v, space, uv_set_name, tolerance
            # 优先用索引定位候选面，命中失败再回退
            u, v = compatibility_utils.uv_sample_at_index(i, num_points, u_center, v_min, v_max, top_uv, bottom_uv)
            pt, left_pt, right_pt, width, height, normal = find_point_and_section_metrics_at_uv(
                mesh_fn, uv_set, u, v, tolerance, index_info
            )
            if pt is not None:
                points_3d.append(pt)
                left_pts.append(left_pt)
                right_pts.append(right_pt)
                widths.append(width)
                heights.append(height)
                normals.append(normal)

        # 对widths进行处理：将每个width限制为不小于最大width的一半
        if widths:
            _max_w = max(widths)
            _min_allowed = _max_w * 0.5
            widths = [w if w >= _min_allowed else _min_allowed for w in widths]

        if len(points_3d) < 2:
            cmds.warning("未能成功采样到足够的点来创建曲线（至少需要2个）。请检查UV布局。")
            return

        # 5. 创建NURBS曲线
        curve_name = compatibility_utils.create_curve_with_name(mesh_transform, curve_degree, points_3d)

        # 5.1 基于第2段的局部坐标系判断左右是否需要整体交换
        # 使用 forward = mid[1]-mid[2], up = normal[1], right = forward x up
        need_swap = False
        try:
            if len(points_3d) >= 3 and normals[1] is not None and left_pts[1] is not None and right_pts[1] is not None:
                f = np.array(points_3d[1]) - np.array(points_3d[2])
                n = np.array(normals[1])
                lf = np.linalg.norm(f)
                ln = np.linalg.norm(n)
                if lf > 1e-8 and ln > 1e-8:
                    f = f / lf
                    n = n / ln
                    r_axis = np.cross(f, n)
                    if np.linalg.norm(r_axis) > 1e-8:
                        lr_vec = np.array(right_pts[1]) - np.array(left_pts[1])
                        if np.dot(lr_vec, r_axis) < 0:
                            need_swap = True
        except Exception:
            need_swap = False

        if need_swap:
            left_pts, right_pts = right_pts, left_pts

        # 6. 聚合数据为样本
        samples: List[SectionSample] = [
            SectionSample(mid=m, left=l, right=r, width=w, height=h, normal=n)
            for m, l, r, w, h, n in zip(points_3d, left_pts, right_pts, widths, heights, normals)
        ]

        return SourceCardData(
            mesh_name=mesh_transform,
            curve_name=curve_name,
            degree=curve_degree,
            samples=samples,
            u_min=u_min,
            u_max=u_max,
            v_min=v_min,
            v_max=v_max
        )

    except Exception as e:
        cmds.error(f"创建曲线时发生错误: {e}")


def create_hair_card_from_curve(source: SourceCardData, num_sides: int) -> GSCard:
    """从源数据创建 GS 发片（曲线网格）。"""
    num_segments = len(source.samples)
    curvename, curvemesh = hair.create_curve_mesh(
        source.curve_name,
        segments=num_segments,
        sides=num_sides,
        is_polygonal=False,
    )
    return GSCard(curve_name=curvename, mesh_name=curvemesh, num_segments=num_segments)


def adjust_segment_widths(gs_card: GSCard, src_card_data: SourceCardData) -> None:
    """调整 GS 发片每段的宽度，使其与源发片匹配。"""
    widths = [s.width for s in src_card_data.samples]
    new_widths = hair.compute_segment_scales_by_uv_grid(gs_card.mesh_name, False)
    # 复用 hair 中的通用构建逻辑，功能保持不变
    scale_curve = hair.make_scale_curve_from_scales(widths, new_widths, simplify=False)
    return scale_curve


def get_continuous_segment_rotations(src_card_data: SourceCardData, gs_card: GSCard) -> List[Tuple[float, float]]:
    if not gs_card.uv_grid:
        gs_card.uv_grid = UVGridCache().get_uv_grid(gs_card.mesh_name, force_update=True)

    new_uv_grid = gs_card.uv_grid
    orig_row_count = len(src_card_data.samples)
    new_row_count = len(new_uv_grid)
    if orig_row_count != new_row_count:
        logging.info("原始和新mesh的uv_grid行数不一致，无法一一对应")
        return []

    # 采样行索引，保持原逻辑 (跳过首尾，使用中间行)
    sample_indices = list(range(1, orig_row_count - 1))

    # 组装原/新剖面向量序列
    zero = om.MVector(0.0, 0.0, 0.0)
    orig_vectors = []
    new_vectors = []
    for row_idx in range(orig_row_count):
        new_row = new_uv_grid[row_idx]
        if len(new_row) < 2:
            logging.info(f"第{row_idx}行剖面点数不足")
            orig_vectors.append(zero)
            new_vectors.append(zero)
            continue
        orig_left_pt = src_card_data.samples[row_idx].left
        orig_right_pt = src_card_data.samples[row_idx].right
        new_left_id = new_row[0]["vtx"]
        new_right_id = new_row[-1]["vtx"]
        orig_vectors.append(om.MVector(*orig_right_pt) - om.MVector(*orig_left_pt))
        new_vectors.append(hair.get_vec(gs_card.mesh_name, new_left_id, new_right_id))

    midpoints = [sample.mid for sample in src_card_data.samples]
    return hair.compute_rotation_curve_from_vectors(orig_vectors, new_vectors, midpoints, sample_indices)


def fit_gs_card_twist_curve(gs_card: GSCard, src_card_data: SourceCardData) -> None:
    """将 GS 发片的扭转曲线逐段拟合到源发片的扭转情况（健壮边界处理）。"""
    twist_curve = get_continuous_segment_rotations(src_card_data, gs_card) or []
    # 需要至少2个点才可安全访问[-2]
    try:
        if len(twist_curve) >= 2 and twist_curve[-2][1] > 1.0:
            last_angle_norm = twist_curve[-2][1]
            last_accum_theta = (last_angle_norm - 0.5) * 360
            hair_utils.set_profile_twist(gs_card.curve_name, last_accum_theta)
            twist_curve = get_continuous_segment_rotations(src_card_data, gs_card) or []
    except Exception as e:
        # 单卡失败时不上抛，交由批处理跳过
        logging.info(f"fit_gs_card_twist_curve fallback: {e}")
        twist_curve = twist_curve if isinstance(twist_curve, list) else []
    # 安全设置图形曲线
    try:
        hair_utils.setSingleGraph("twist", twist_curve)
        hair_utils.setCurveGraphAttr("twist", twist_curve)
    except Exception as e:
        logging.info(f"set twist curve failed: {e}")


def fit_gs_card_orientation(gs_card: GSCard, src_card_data: SourceCardData) -> None:
    """将 GS 发片的根部朝向对齐到源发片。

    参考 get_continuous_segment_rotations：取中线切线作为旋转轴，将剖面向量
    投影到垂直于该轴的平面上，计算“绕轴的有符号角”（不做 unwrap，不加 180°）。
    """
    # 确保 uv_grid 可用
    if gs_card.uv_grid is None:
        gs_card.uv_grid = UVGridCache().get_uv_grid(gs_card.mesh_name, force_update=True)

    total = len(src_card_data.samples)
    if total < 2:
        return

    # Use row at 30%（保持现有取样策略）
    row_id = int(total * ROOT_RATIO)
    if row_id >= total - 1:
        row_id = total - 2  # 确保 row_id+1 有效

    # 新 gs 发片该行的剖面向量
    new_root_left_id, new_root_right_id = hair.get_profile_indices_by_uv_grid(gs_card.uv_grid, row_id)
    new_root_vec = hair.get_vec(gs_card.mesh_name, new_root_left_id, new_root_right_id)

    # 源发片该行的剖面向量与切线轴（由相邻中线点构成）
    root_vec = om.MVector(*src_card_data.samples[row_id].right) - om.MVector(*src_card_data.samples[row_id].left)
    up = om.MVector(*src_card_data.samples[row_id + 1].mid) - om.MVector(*src_card_data.samples[row_id].mid)
    axis = up.normal() if up.length() > 1e-6 else om.MVector(0, 1, 0)

    if root_vec.length() > 1e-6 and new_root_vec.length() > 1e-6 and axis.length() > 1e-6:
        # 使用“绕 axis 的有符号角”，与 get_continuous_segment_rotations 的定义一致
        rot_angle = hair.signed_angle_around_axis(axis, -new_root_vec, root_vec)
        hair_utils.set_profile_orientation(gs_card.curve_name, rot_angle)


def fit_gs_card_to_hair_card(gs_card: GSCard, src_card_data: SourceCardData) -> None:
    hair_utils.setFlipUV(gs_card.curve_name, 1)
    hair_utils.setCurveRefine(gs_card.num_segments)

    # 确保 uv_grid 可用
    if gs_card.uv_grid is None:
        gs_card.uv_grid = UVGridCache().get_uv_grid(gs_card.mesh_name, force_update=True)

    # 设置宽度（保持旧逻辑：用源的 root 宽度与新 root 宽度之比缩放）
    if not src_card_data.samples:
        return
    widths = [s.width for s in src_card_data.samples]
    new_root_width = hair.get_root_width(gs_card.mesh_name, gs_card.uv_grid, False)
    # Use width at 30% as root width
    width_id = int(len(widths) * ROOT_RATIO)
    scale_factor = widths[width_id] / new_root_width if new_root_width not in (0, None) else 1.0
    hair_utils.set_curve_width(gs_card.curve_name, scale_factor, False)

    # 设置根部朝向
    fit_gs_card_orientation(gs_card, src_card_data)
    # 设置整体及每一段扭转
    fit_gs_card_twist_curve(gs_card, src_card_data)
    # 设置每一段宽度
    adjust_segment_widths(gs_card, src_card_data)
    # 设置UV缩放
    uv_transform = hair_utils.uv_transform_from_bounds(
        src_card_data.u_min,
        src_card_data.u_max,
        src_card_data.v_min,
        src_card_data.v_max
    )
    hair_utils.setUVTransform(uv_transform)
    # 设置材质
    hair.assign_existing_material(src_card_data.mesh_name, gs_card.mesh_name)


def convert_single_hair_card_to_gs_card():
    mesh_transform = compatibility_utils.get_selected_mesh_transform()
    src_card_data = create_centerline_curve_from_mesh(mesh_transform)
    if not src_card_data or not src_card_data.samples:
        cmds.warning("未能生成源数据，请检查选择对象与其 UV/几何是否有效。")
        return
    gs_card = create_hair_card_from_curve(src_card_data, num_sides=2)
    fit_gs_card_to_hair_card(gs_card, src_card_data)


def convert_selected_hair_cards_to_gs_cards_batch(progress_bar=None, progress_text=None):
    """兼容模式批量转换（复用通用批处理编排）。"""
    def per_mesh_convert(mesh):
        src_card_data = create_centerline_curve_from_mesh(mesh)
        if not src_card_data or not src_card_data.samples:
            return None
        gs_card = create_hair_card_from_curve(src_card_data, num_sides=2)
        fit_gs_card_to_hair_card(gs_card, src_card_data)

        return gs_card.curve_name

    hair.batch_convert_selected(per_mesh_convert, progress_bar=progress_bar, progress_text=progress_text)
