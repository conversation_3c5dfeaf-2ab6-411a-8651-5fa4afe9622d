# Import built-in modules
import logging
import math

# Import third-party modules
from blade_client_reporter import get_reporter
import maya.api.OpenMaya as om
import maya.cmds as cmds
import numpy as np
from scipy.interpolate import CubicSpline
from scipy.optimize import minimize
from scipy.signal import argrelextrema

# Import local modules
from maya_gs_coverter import core
import maya_gs_coverter.compatability_mode.converter as compatability_converter
import maya_gs_coverter.utils.utils as utils
from maya_gs_coverter.uv_grid_cache import UVGridCache


def ensure_plugin_loaded(plugin_name):
    # 检查插件是否已加载
    if not cmds.pluginInfo(plugin_name, query=True, loaded=True):
        try:
            cmds.loadPlugin(plugin_name)
            print("插件 {} 已加载。".format(plugin_name))
        except Exception as e:
            cmds.warning("无法加载插件 {}: {}".format(plugin_name, e))


def recover_spline_nodes_auto(result, r2_threshold=0.98, N_min=5, N_max=None):
    """
    自动寻找满足阈值的最小节点数，使三次样条插值的 R² ≥ r2_threshold。

    Args:
        result (list[tuple[float, float]]): 采样点 (t, y) 列表。
        r2_threshold (float, optional): 拟合优度阈值。默认 0.98。
        N_min (int, optional): 最小节点数。默认 5。
        N_max (int | None, optional): 最大节点数。默认与采样点数相同。

    Returns:
        list[tuple[float, float]]: 节点 (t, y) 列表。
    """
    result = np.array(result)
    t_samples = result[:, 0]
    y_samples = result[:, 1]
    if N_max is None:
        N_max = len(t_samples)

    def fit_and_r2(N):
        # 初始猜测：等间距采样点
        init_idx = np.linspace(0, len(t_samples) - 1, N).astype(int)
        t_nodes = t_samples[init_idx]
        y_nodes_init = y_samples[init_idx]

        def loss(y_nodes):
            cs = CubicSpline(t_nodes, y_nodes)
            y_fit = cs(t_samples)
            return np.sum((y_fit - y_samples) ** 2)

        res = minimize(loss, y_nodes_init, method="L-BFGS-B")
        y_nodes_opt = res.x
        cs = CubicSpline(t_nodes, y_nodes_opt)
        y_fit = cs(t_samples)
        # 计算R²
        ss_res = np.sum((y_samples - y_fit) ** 2)
        ss_tot = np.sum((y_samples - np.mean(y_samples)) ** 2)
        r2 = 1 - ss_res / ss_tot if ss_tot > 0 else 1.0
        nodes = list(zip(t_nodes.tolist(), y_nodes_opt.tolist()))
        return nodes, r2

    for N in range(N_min, N_max + 1):
        nodes, r2 = fit_and_r2(N)
        print(nodes, r2)
        if r2 >= r2_threshold:
            return nodes

    # 如果到最大节点数还不满足，返回最大节点数的结果
    nodes, r2 = fit_and_r2(N_max)
    return nodes


def recover_spline_nodes(result, N=5):
    """
    根据采样点反推三次样条插值的必经节点点。

    Args:
        result (list[tuple[float, float]]): 采样点 (t, y) 列表。
        N (int, optional): 节点点数量。默认 5。

    Returns:
        list[tuple[float, float]]: 节点 (t, y) 列表。
    """
    result = np.array(result)
    t_samples = result[:, 0]
    y_samples = result[:, 1]

    # 初始猜测：等间距采样点
    init_idx = np.linspace(0, len(t_samples) - 1, N).astype(int)
    t_nodes = t_samples[init_idx]
    y_nodes_init = y_samples[init_idx]

    def loss(y_nodes):
        cs = CubicSpline(t_nodes, y_nodes)
        y_fit = cs(t_samples)
        return np.sum((y_fit - y_samples) ** 2)

    res = minimize(loss, y_nodes_init, method="L-BFGS-B")
    y_nodes_opt = res.x

    # 返回格式与result一致
    nodes = list(zip(t_nodes.tolist(), y_nodes_opt.tolist()))
    return nodes


def get_selected_meshes():
    # 获取所有选中的 mesh 物体
    selection = cmds.ls(selection=True, long=True, dag=True, type="mesh")
    # 获取 mesh 的 transform 父节点
    transforms = list(set(cmds.listRelatives(selection, parent=True, fullPath=True) or []))
    return transforms


def separate_mesh(mesh):
    """
    分离一个 Mesh 为独立的片段。

    Args:
        mesh (str): Mesh 的 transform 名称。

    Returns:
        list[str] | tuple[list[str], str]: 分离出的 mesh transform 列表；若仅一片，返回 [mesh]。
            部分情况下也可能返回 (separated_meshes, mesh)。
    """
    try:
        cmds.select(mesh, replace=True)
        separated_groups = cmds.polySeparate(mesh, constructionHistory=False)
        # polySeparate 返回的是分离后所有新生成的 transform 节点（通常是组）

        separated_meshes = []
        for group in separated_groups:
            # 获取组下所有的 mesh transform
            children = cmds.listRelatives(group, children=True, type="transform", fullPath=True) or []
            # 只添加有mesh shape的transform
            for child in children:
                shapes = cmds.listRelatives(child, shapes=True, type="mesh", fullPath=True) or []
                if shapes:
                    separated_meshes.append(child)
            # 有些maya版本polySeparate直接返回的就是transform，不是组
            shapes = cmds.listRelatives(group, shapes=True, type="mesh", fullPath=True) or []
            if shapes:
                separated_meshes.append(group)
        # 去重
        separated_meshes = list(set(separated_meshes))
        return (separated_meshes, mesh) if separated_meshes else [mesh]
    except Exception as e:
        # 如果polySeparate报错，说明只有一片，直接返回原mesh
        return [mesh]


def get_hair_params(mesh):
    """
    获取多边形毛发的参数：段数、每段边数、每段顶点 id 列表。

    Args:
        mesh (str): Mesh transform 或 shape 名称。

    Returns:
        tuple: (num_segments, num_sides, uv_grid, mesh_fn)
            num_segments (int): 段数。
            num_sides (int): 每段边数。
            uv_grid (list[list[dict]]): UV 网格。
            mesh_fn (om.MFnMesh): 网格函数集。
    """
    mesh_fn = get_mesh_fn(mesh)
    uv_grid = UVGridCache().get_uv_grid(mesh)
    num_segments = len(uv_grid)
    num_sides = len(uv_grid[0]) if uv_grid else 0
    return num_segments, num_sides, uv_grid, mesh_fn


def create_curve_mesh(curve, segments=5, sides=0, is_polygonal=False):
    # sides=0: 线状，sides>0: 多边形
    cmds.select(curve, r=True)
    if is_polygonal:
        core.Create().multiple(1)
    else:
        core.Create().multiple(0)
    curve_res = cmds.ls(sl=True)[0]
    cmds.setAttr(curve_res + ".lengthDivisions", segments)
    cmds.setAttr(curve_res + ".widthDivisions", sides)
    curve_mesh = get_curve_mesh_by_curve(curve_res)
    return curve_res, curve_mesh


def get_curve_mesh_by_curve(curve_res):
    curve_mesh = None
    parent = cmds.listRelatives(curve_res, parent=True, fullPath=True)
    if not parent:
        cmds.warning("该节点没有父节点")
    else:
        parent = parent[0]
        siblings = cmds.listRelatives(parent, children=True, type="transform", fullPath=True) or []
        for sib in siblings:
            shapes = cmds.listRelatives(sib, shapes=True, fullPath=True) or []
            for shape in shapes:
                if cmds.nodeType(shape) == "mesh":
                    curve_mesh = sib
                    break
    return curve_mesh


def match_vertices(orig_mesh, new_mesh):
    """
    将 new_mesh 的边界顶点位置与法线对齐到 orig_mesh 的边界顶点。

    对多边形毛发，使用 UV 排序后的位置一一对应。

    Args:
        orig_mesh (str): 原始 mesh。
        new_mesh (str): 目标 mesh。
    """
    uv_cache = UVGridCache()
    orig_uv_grid = uv_cache.get_uv_grid(orig_mesh)
    new_uv_grid = uv_cache.get_uv_grid(new_mesh)

    # flatten uv_grid，得到有序顶点索引列表
    orig_indices = [item["vtx"] for row in orig_uv_grid for item in row]
    new_indices = [item["vtx"] for row in new_uv_grid for item in row]

    if len(orig_indices) != len(new_indices):
        cmds.warning("新旧mesh顶点数量不符，无法自动匹配")
        return

    # 获取MFnMesh对象
    get_mesh_fn(orig_mesh)
    get_mesh_fn(new_mesh)

    # 获取原mesh所有顶点世界坐标
    orig_positions = []
    for i in range(cmds.polyEvaluate(orig_mesh, v=True)):
        pos = cmds.xform("{}.vtx[{}]".format(orig_mesh, i), q=True, ws=True, t=True)
        orig_positions.append(pos)

    # 获取原mesh所有顶点法线
    orig_normals = []
    for i in range(cmds.polyEvaluate(orig_mesh, v=True)):
        normals = cmds.polyNormalPerVertex("{}.vtx[{}]".format(orig_mesh, i), q=True, xyz=True)
        orig_normals.append(normals[:3])

    # 设置新mesh顶点坐标
    for src, tgt in zip(orig_indices, new_indices):
        pos = orig_positions[src]
        cmds.xform("{}.vtx[{}]".format(new_mesh, tgt), ws=True, t=pos)

    # 设置新mesh顶点法线
    for src, tgt in zip(orig_indices, new_indices):
        n = orig_normals[src]
        cmds.polyNormalPerVertex("{}.vtx[{}]".format(new_mesh, tgt), xyz=n)


def create_middle_curve(mid_points):
    curve = cmds.curve(degree=3, editPoint=mid_points)
    return curve


def process(*args, **kwargs):
    # 统一通过通用批处理进行编排，仅提供单卡转换回调
    ensure_plugin_loaded("curveWarp.mll")
    do_vertex_match = kwargs.get("do_vertex_match", False)
    progress_bar = kwargs.get("progress_bar", None)
    progress_text = kwargs.get("progress_text", None)
    only_create_curve = kwargs.get("only_create_curve", False)

    def per_mesh_convert(mesh, only_create_curve=only_create_curve):
        curve_initial, is_poly, mid_points = extract_curve_from_mesh(mesh)
        if not curve_initial:
            return None
        if only_create_curve:
            return curve_initial
        curve_final, _ = generate_gs_hair_and_match(
            mesh, curve_initial, is_poly, mid_points, do_vertex_match=do_vertex_match
        )
        return curve_final

    batch_convert_selected(per_mesh_convert, progress_bar=progress_bar, progress_text=progress_text)
    logging.info("处理完成")


def extract_curve_from_mesh(mesh):
    """
    第一步：提取中线并创建曲线（执行到 create_middle_curve）。

    Args:
        mesh (str): 输入 mesh 名称。

    Returns:
        tuple[str | None, bool | None, list | None]: (曲线名, 是否为多边形, 中线点列表)。失败返回 (None, None, None)。
    """
    is_poly = UVGridCache.is_polygonal_hair(mesh)
    if is_poly is None:
        logging.warning(f"面片{mesh}不符合规范，请检查！")
        return None, None, None

    mid_points = UVGridCache().get_middle_line_points(mesh)
    if len(mid_points) < 2:
        cmds.warning("无法找到中线点，请检查模型结构")
        return None, None, None

    curvename = create_middle_curve(mid_points)
    return curvename, is_poly, mid_points


def generate_gs_hair_and_match(mesh, curvename, is_poly, mid_points, do_vertex_match=True):
    """
    第二步：生成GS发片（基于已有中线曲线）并匹配原发片。

    Args:
        mesh (str): 输入 mesh 名称。
        curvename (str): 第一步创建的中线曲线名。
        is_poly (bool): 是否为多边形毛发。
        mid_points (list[tuple[float, float, float]]): 中线点列表。
        do_vertex_match (bool): 是否进行逐顶点匹配。

    Returns:
        tuple[str | None, str | None]: (曲线名, 处理后 mesh 名)。失败返回 (None, None)。
    """
    if not curvename or mid_points is None or is_poly is None:
        return None, None

    num_segments, num_sides, v_groups, _ = get_hair_params(mesh)
    curvename, curvemesh = create_curve_mesh(
        curvename,
        segments=len(mid_points),
        sides=num_sides,
        is_polygonal=is_poly,
    )
    new_mesh = curvemesh

    utils.setFlipUV(curvename, 1)
    utils.setCurveRefine(num_segments)

    uv_grid = UVGridCache().get_uv_grid(mesh)
    new_uv_grid = UVGridCache().get_uv_grid(new_mesh, force_update=True)

    root_width = get_root_width(mesh, uv_grid, is_poly)
    new_root_width = get_root_width(new_mesh, new_uv_grid, is_poly)

    scale_factor = root_width / new_root_width
    utils.set_curve_width(curvename, scale_factor, is_poly)

    if not is_poly:
        twist_curve = update_profile_orientation_and_twist(
            mesh, new_mesh, uv_grid, new_uv_grid, mid_points, curvename, utils
        )
        utils.setCurveGraphAttr("twist", twist_curve)
        scale_curve = adjust_scale_curve(mesh, new_mesh, is_poly)
        utils.setCurveGraphAttr("scale", scale_curve)
        profile_result, profile_value = update_profile_value_by_v_shape(
            mesh,
            scale_curve,
            mid_points,
            uv_grid,
            core,
            utils,
            V_SHAPE_THRESHOLD=0.05,
        )
        if profile_result:
            utils.setCurveGraphAttr("profile", profile_result)
        need_reverse = reverse_new_mesh_normals_if_needed(mesh, new_mesh, uv_grid, new_uv_grid)

        if need_reverse and profile_result:
            profile_value = -profile_value
            utils.setProfile(profile_value)

        if do_vertex_match:
            match_vertices(mesh, new_mesh)
        uv_transform = UVGridCache().get_uv_transform(mesh)
        utils.setUVTransform(uv_transform)
    else:
        adjust_scale_curve(mesh, new_mesh, is_poly)

    assign_existing_material(mesh, new_mesh)
    UVGridCache().clear_cache()
    return curvename, new_mesh


def batch_convert_selected(per_mesh_convert, progress_bar=None, progress_text=None):
    """
    清晰简洁版批处理：
      1) 检查插件与选择
      2) 复制并隐藏原件，复制体切换到有效UV集
      3) 冻结变换并删除历史
      4) 按需拆分并记录归组关系
      5) 逐卡处理：调用 per_mesh_convert，重命名并归组
      6) 收尾：重命名分离组，清理临时体，更新进度

    per_mesh_convert: Callable[[str], Optional[str]]
        入参为单个拆分后的 mesh transform 名称；
        返回用于分组与命名的曲线名（其父组会被重命名为 orig+"_warp"）；返回 None 则跳过分组。

    """
    # 0) 插件（幂等）
    try:
        ensure_plugin_loaded("curveWarp.mll")
    except Exception:
        pass

    # 1) 选择
    mesh_list = get_selected_meshes()
    if not mesh_list:
        cmds.warning("请先选中一个或多个毛发面片对象！")
        return

    # 2) 复制并隐藏原件，记录原父级
    copied_meshes = []
    orig_parents = {}
    for mesh in mesh_list:
        copy = cmds.duplicate(mesh, name=mesh + "_copy")[0]
        utils.set_valid_uv_set(copy)
        copied_meshes.append(copy)
        parent = cmds.listRelatives(mesh, parent=True, fullPath=True)
        orig_parents[copy] = parent[0] if parent else None
        try:
            cmds.hide(mesh)
        except Exception:
            pass

    # 3) 冻结与删历史
    for mesh in copied_meshes:
        try:
            cmds.makeIdentity(mesh, apply=True, t=1, r=1, s=1, n=0)
            cmds.delete(mesh, constructionHistory=True)
        except Exception:
            pass

    # 4) 拆分
    mesh_to_newgroup = {}
    processed_meshes = []
    separated_groups = set()
    for mesh in copied_meshes:
        result = separate_mesh(mesh)
        if isinstance(result, tuple):
            separated_meshes, new_group = result
        else:
            separated_meshes, new_group = result, None
        processed_meshes.extend(separated_meshes)
        if new_group:
            for m in separated_meshes:
                mesh_to_newgroup[m] = new_group
            separated_groups.add(new_group)

    total_count = len(processed_meshes)
    utils.init_progress_ui(progress_bar, progress_text, total_count)

    # 5) 逐卡处理
    for i, mesh in enumerate(processed_meshes, start=1):
        try:
            cmds.delete(mesh, constructionHistory=True)
        except Exception:
            pass

        curve_name = None
        try:
            curve_name = per_mesh_convert(mesh)
        except Exception as e:
            logging.info(f"单卡处理失败: {mesh}: {e}")
            curve_name = None

        if curve_name:
            # 若曲线已有父组，则按常规流程重命名父组并以父组为单位归组；
            # 若没有父组，则直接将曲线本身归入 polySeparate 新组或原父级，不再创建“原名_warp”小组。
            group = cmds.listRelatives(curve_name, parent=True, fullPath=True)
            target = None
            if group:
                group = group[0]
                # 仅当已有父组时才重命名为“原名_warp”
                orig_name = mesh[:-5] if mesh.endswith("_copy") else mesh
                new_group_name = orig_name + "_warp"
                try:
                    target = cmds.rename(group, new_group_name)
                except Exception:
                    target = group
            else:
                target = curve_name

            # 若来自 polySeparate，则归入其新组；否则归回原父级；若两者皆无则保持在世界层级
            if mesh in mesh_to_newgroup:
                try:
                    cmds.parent(target, mesh_to_newgroup[mesh])
                except Exception:
                    pass
            else:
                orig_parent = orig_parents.get(mesh)
                if orig_parent:
                    try:
                        cmds.parent(target, orig_parent)
                    except Exception:
                        pass

        utils.update_progress_ui(progress_bar, progress_text, i, total_count)

    # 6) 收尾
    for group in separated_groups:
        if group.endswith("_copy"):
            new_name = group[:-5]
            if cmds.objExists(new_name):
                new_name = new_name + "_renamed"
            try:
                cmds.rename(group, new_name)
            except Exception as e:
                logging.info(f"重命名组 {group} 失败: {e}")

    for mesh in processed_meshes:
        try:
            if cmds.objExists(mesh):
                cmds.delete(mesh)
        except Exception:
            pass
    for mesh in copied_meshes:
        try:
            if cmds.objExists(mesh):
                cmds.delete(mesh)
        except Exception:
            pass

    utils.finish_progress_ui(progress_bar, progress_text, total_count)


def simplify_curve_graph(graph):
    sel = cmds.filterExpand(cmds.ls(sl=1, tr=1), sm=9)
    curve = sel[-1]
    attributes = utils.getGraphAttrName(graph)
    input_string = utils.getAttr(curve, attributes)
    valuelist = utils.fromStringToDouble2(input_string)
    result = recover_spline_nodes_auto(valuelist)
    utils.setCurveGraphAttr(graph, result)
    if graph == "profile":
        core.Create.setProfileCurve(result)
    else:
        utils.resetSingleGraphValues(graph)
        utils.setSingleGraph(graph, result)
    logging.info(f"简化完成 {curve}.{attributes}.{valuelist}")


def generate_lod(curve_name, percent):
    """
    根据百分比调整曲线的 lengthDivisions（至少为 2）。

    Args:
        curve_name (str): 曲线 transform 或 shape 名称。
        percent (int): 百分比，范围 0-200。
    """
    get_curve_mesh_by_curve(curve_name)
    if not cmds.attributeQuery("lengthDivisions", node=curve_name, exists=True):
        cmds.warning("节点{}没有lengthDivisions属性".format(curve_name))
        return
    current_div = cmds.getAttr(curve_name + ".lengthDivisions")
    new_div = int(round(current_div * percent / 100.0))
    new_div = max(new_div, 2)
    cmds.setAttr(curve_name + ".lengthDivisions", new_div)
    print("{}: lengthDivisions {} -> {}".format(curve_name, current_div, new_div))


def get_v_shape_value_by_row(mesh, row):
    """
    计算一行（剖面）的 V 形值。

    Args:
        mesh (str): Mesh 名称。
        row (list[dict]): uv_grid 中的一行（每个 dict 至少包含 'vtx' 键）。

    Returns:
        float: V 形值。
    """
    if len(row) < 3:
        return 0.0
    vtx_ids = [item["vtx"] for item in row]
    left = vtx_ids[0]
    right = vtx_ids[-1]
    left_pos = get_vertex_position(mesh, left)
    right_pos = get_vertex_position(mesh, right)
    line_vec = [r - l for r, l in zip(right_pos, left_pos)]
    line_length = sum((x**2 for x in line_vec)) ** 0.5
    if line_length < 1e-6:
        return 0.0
    line_dir = [x / line_length for x in line_vec]
    max_dist = 0.0
    for idx in vtx_ids[1:-1]:
        pt = get_vertex_position(mesh, idx)
        vec = [p - l for p, l in zip(pt, left_pos)]
        proj = sum(v * d for v, d in zip(vec, line_dir))
        closest = [left_pos[i] + proj * line_dir[i] for i in range(3)]
        dist_vec = [pt[i] - closest[i] for i in range(3)]
        dist = sum(x**2 for x in dist_vec) ** 0.5
        # 这里不区分正负，按绝对值
        if abs(dist) > abs(max_dist):
            max_dist = dist
    return max_dist


def v_shape_match_result(mesh, scale_curve, mid_points, V_SHAPE_THRESHOLD=0.05):
    """
    使用 uv_grid 检测 V 形结构，并进行 V 形匹配。

    Args:
        mesh (str): Mesh 名称。
        scale_curve (list[tuple[float, float]]): scale 曲线 (pos, value)。
        mid_points (list[list[float]]): 中线点列表。
        V_SHAPE_THRESHOLD (float, optional): V 形阈值。默认 0.05。

    Returns:
        list[tuple[float, float]]: result 列表 (pos, value)。
    """
    uv_cache = UVGridCache()
    uv_grid = uv_cache.get_uv_grid(mesh)
    if not uv_grid or len(uv_grid) == 0:
        return None

    # 1. 计算所有剖面的V形值
    v_shape_values = [get_v_shape_value_by_row(mesh, row) for row in uv_grid]
    avg_v_shape = sum(abs(v) for v in v_shape_values) / len(v_shape_values) if v_shape_values else 0.0

    if avg_v_shape > V_SHAPE_THRESHOLD:
        result = []
        positions = utils.get_normalized_curve_positions(mid_points)
        for i in range(len(scale_curve)):
            recover_value = 0.5 / scale_curve[i][1]
            # v_shape_values和scale_curve长度可能不一致，做安全处理
            v_shape = v_shape_values[i] if i < len(v_shape_values) else 0.0
            result.append((positions[i], v_shape * recover_value))
        return result
    else:
        return None


def get_vec(mesh, id1, id2):
    pos1 = get_vertex_position(mesh, id1)
    pos2 = get_vertex_position(mesh, id2)
    return pos2 - pos1


def get_vertex_position(mesh, vtx_id):
    try:
        mesh_fn = get_mesh_fn(mesh)
        pos = mesh_fn.getPoint(vtx_id, om.MSpace.kWorld)
        return om.MVector(pos)
    except Exception as e:
        logging.info("get_vec error:", e)
        cmds.warning(f"get_vec: 获取{mesh}点坐标失败，id={vtx_id}")
        return om.MVector(0.0, 0.0, 0.0)  # 返回零向量


def compute_rotation_curve_from_vectors(orig_vectors, new_vectors, midpoints, sample_indices):
    """
    基于每行的原/新剖面向量，计算连续的旋转曲线。

    Args:
        orig_vectors (list[om.MVector]): 原剖面向量（每行一个）。
        new_vectors (list[om.MVector]): 新剖面向量（每行一个）。
        midpoints (list[list[float]]): 中线点 [[x, y, z], ...]。
        sample_indices (list[int]): 需要采样的行索引。

    Returns:
        list[tuple[float, float]]: (pos, angle_norm) 列表。
    """
    orig_row_count = len(orig_vectors)
    result = []
    prev_accum_theta = None

    for row_idx in sample_indices:
        vec = orig_vectors[row_idx]
        new_vec = new_vectors[row_idx]
        if vec.length() < 1e-6 or new_vec.length() < 1e-6:
            logging.info(f"第{row_idx}行剖面向量长度为0，跳过")
            continue

        axis = compute_tangent(midpoints, row_idx)
        if axis is None:
            axis = om.MVector(0, 1, 0)

        theta = signed_angle_around_axis(axis, new_vec, vec)
        if theta is None:
            logging.info(f"第{row_idx}行投影退化，无法计算角度，使用0或前一值")
            theta = 0.0

        # 累积旋转角度处理
        if prev_accum_theta is None:
            accum_theta = 0
        else:
            candidates = [theta, theta + 180, theta - 180, theta + 360, theta - 360]
            accum_theta = min(candidates, key=lambda x: abs(x - prev_accum_theta))
        prev_accum_theta = accum_theta

        angle_norm = 0.5 + (accum_theta / 360.0)

        pos = row_idx / (orig_row_count - 1) if orig_row_count > 1 else 0.0
        result.append((pos, angle_norm))

    return result



def get_profile_rotation_list_smooth(orig_mesh, new_mesh, midpoints, num_samples=10):
    """
    采样剖面旋转差异，返回标准化角度曲线（使用 uv_grid 优化计算）。

    Args:
        orig_mesh (str): 原始 mesh。
        new_mesh (str): 新 mesh。
        midpoints (list[list[float]]): 中线点 [[x, y, z], ...]。
        num_samples (int, optional): 采样点数量。默认 10。

    Returns:
        list[tuple[float, float]]: (pos, angle_norm) 列表。
    """
    orig_uv_grid = UVGridCache().get_uv_grid(orig_mesh)
    new_uv_grid = UVGridCache().get_uv_grid(new_mesh)
    # 采样点在uv_grid中的行索引
    orig_row_count = len(orig_uv_grid)
    new_row_count = len(new_uv_grid)
    if orig_row_count != new_row_count:
        logging.info("原始和新mesh的uv_grid行数不一致，无法一一对应")
        return []

    # 采样num_samples个点，均匀分布在行索引上
    sample_indices = np.linspace(0, orig_row_count - 1, num_samples).astype(int).tolist()

    # 为每一行准备剖面向量（不足两点时置零向量，保持原逻辑等价：随后会被跳过）
    zero = om.MVector(0.0, 0.0, 0.0)
    orig_vectors = []
    new_vectors = []
    for row_idx in range(orig_row_count):
        orig_row = orig_uv_grid[row_idx]
        new_row = new_uv_grid[row_idx]
        if len(orig_row) < 2 or len(new_row) < 2:
            logging.info(f"第{row_idx}行剖面点数不足")
            orig_vectors.append(zero)
            new_vectors.append(zero)
            continue
        orig_left_id = orig_row[0]["vtx"]
        orig_right_id = orig_row[-1]["vtx"]
        new_left_id = new_row[0]["vtx"]
        new_right_id = new_row[-1]["vtx"]
        orig_vectors.append(get_vec(orig_mesh, orig_left_id, orig_right_id))
        new_vectors.append(get_vec(new_mesh, new_left_id, new_right_id))

    return compute_rotation_curve_from_vectors(orig_vectors, new_vectors, midpoints, sample_indices)


def compute_tangent(midpoints, i):
    EPS = 1e-6
    n = len(midpoints)
    if midpoints[i] is None:
        return None
    if i < n - 1 and midpoints[i + 1] is not None:
        t = om.MVector(*midpoints[i + 1]) - om.MVector(*midpoints[i])
    elif i > 0 and midpoints[i - 1] is not None:
        t = om.MVector(*midpoints[i]) - om.MVector(*midpoints[i - 1])
    else:
        return None
    if t.length() < EPS:
        return None
    return t.normal()


def signed_angle_around_axis(axis, v_from, v_to):
    EPS = 1e-6
    proj_from = v_from - axis * (v_from * axis)
    proj_to = v_to - axis * (v_to * axis)
    lf = proj_from.length()
    lt = proj_to.length()
    if lf < EPS or lt < EPS:
        return None  # 退化，无法定义
    proj_from *= 1.0 / lf
    proj_to *= 1.0 / lt
    # 有符号角度： atan2(axis·(from×to), from·to)
    cross = proj_from ^ proj_to  # om.MVector cross
    sinv = axis * cross
    cosv = proj_from * proj_to
    ang = math.degrees(math.atan2(sinv, cosv))
    return ang


def get_vertex_normal(mesh, vtx_id):
    """
    获取 mesh 某个顶点的平均法线。

    Args:
        mesh (str): mesh 名称。
        vtx_id (int): 顶点索引。

    Returns:
        om.MVector: 顶点平均法线。
    """
    # getVertexNormal 返回 om.MVector
    mesh_fn = get_mesh_fn(mesh)
    normal = mesh_fn.getVertexNormal(vtx_id, True)  # True 表示空间法线（平均法线）
    return normal


def generate_callback(*args):
    global percent_slider
    percent = cmds.intSliderGrp(percent_slider, q=True, value=True)
    selection = cmds.ls(sl=True)
    if not selection:
        cmds.warning("未选择任何对象！")
        return


    curves_to_process = set()

    for obj in selection:
        if cmds.objectType(obj, isType="transform"):
            children = cmds.listRelatives(obj, children=True, fullPath=True) or []
            for child in children:
                if cmds.objectType(child, isType="nurbsCurve"):  # 是shape
                    parent = cmds.listRelatives(child, parent=True, fullPath=True)[0]
                    curves_to_process.add(parent)
                elif cmds.objectType(child, isType="transform"):
                    shape = cmds.listRelatives(child, shapes=True, fullPath=True)
                    if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                        curves_to_process.add(child)
            shape = cmds.listRelatives(obj, shapes=True, fullPath=True)
            if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                curves_to_process.add(obj)

        elif cmds.objectType(obj, isType="nurbsCurve"):
            parent = cmds.listRelatives(obj, parent=True, fullPath=True)[0]
            curves_to_process.add(parent)

    if not curves_to_process:
        cmds.warning("未找到可处理的曲线！")
        return

    for curve in curves_to_process:
        generate_lod(curve, percent)


def adjust_scale_curve(orig_mesh, new_mesh, is_poly):
    # 1. 计算每段缩放
    scales = compute_segment_scales_by_uv_grid(orig_mesh, is_poly)
    new_scales = compute_segment_scales_by_uv_grid(new_mesh, is_poly)
    if is_poly:
        logging.info(f"poly mesh, simplify scales")
    # 复用通用构建逻辑；poly 情况下做简化处理，与原逻辑一致
    return make_scale_curve_from_scales(scales, new_scales, simplify=bool(is_poly))


def simplify_scales(scales, tolerance=0.05):
    """
    简化缩放序列，保留重要特征点。

    Args:
        scales (list[float]): 缩放系数列表。
        tolerance (float, optional): 允许的最大误差。默认 0.05。

    Returns:
        tuple[list[int], list[float]]: (简化后的索引列表, 简化后的缩放系数)
    """

    scales = np.array(scales)
    # 找极值点
    maxima = argrelextrema(scales, np.greater)[0]
    minima = argrelextrema(scales, np.less)[0]
    key_points = set([0, len(scales) - 1]) | set(maxima) | set(minima)
    key_points = sorted(list(key_points))
    # 进一步筛选，去掉变化小的点
    filtered = [key_points[0]]
    for i in range(1, len(key_points)):
        if abs(scales[key_points[i]] - scales[filtered[-1]]) > tolerance:
            filtered.append(key_points[i])
    if filtered[-1] != key_points[-1]:
        filtered.append(key_points[-1])
    return filtered, [scales[i] for i in filtered]


def normalize_scales(orig_scales, new_scales):
    return [0.5 * (o / c) if c != 0 else 0.5 for o, c in zip(orig_scales, new_scales)]


def build_scale_curve_from_norm_scales(norm_scales):
    n = len(norm_scales)
    positions = [i / (n - 1) if n > 1 else 0.0 for i in range(n)]
    valueList = list(zip(positions, norm_scales))
    scale_curve = valueList
    utils.setSingleGraph("scale", scale_curve)
    return scale_curve


def make_scale_curve_from_scales(orig_scales, new_scales, simplify=False):
    norm_scales = normalize_scales(orig_scales, new_scales)
    if simplify:
        _, norm_scales = simplify_scales(norm_scales)
    return build_scale_curve_from_norm_scales(norm_scales)


def compute_segment_scales_by_uv_grid(mesh, is_poly):
    mesh_fn = get_mesh_fn(mesh)
    uv_grid = UVGridCache().get_uv_grid(mesh)
    scales = []
    for row in uv_grid:
        if is_poly:
            # 平均半径
            pts = [om.MVector(mesh_fn.getPoint(item["vtx"], om.MSpace.kWorld)) for item in row]
            mid = sum(pts, om.MVector(0, 0, 0)) * (1.0 / len(pts))
            avg_radius = sum([(p - mid).length() for p in pts]) / len(pts)
            scales.append(avg_radius)
        else:
            # 线状，左右两点距离
            left = om.MVector(mesh_fn.getPoint(row[0]["vtx"], om.MSpace.kWorld))
            right = om.MVector(mesh_fn.getPoint(row[-1]["vtx"], om.MSpace.kWorld))
            scales.append((left - right).length())
    return scales


def get_root_width(mesh, uv_grid, is_poly=False):
    """
    获取原始 mesh 根部宽度。

    Args:
        mesh (str): Mesh 名称。
        uv_grid (list[list[dict]]): UV 网格。
        is_poly (bool, optional): 是否为多边形毛发。默认 False。

    Returns:
        float: 宽度；多边形返回平均半径的两倍，线状为左右两点距离。
    """
    if not uv_grid or not uv_grid[0]:
        return 1.0
    row = uv_grid[0]
    mesh_fn = get_mesh_fn(mesh)
    if is_poly:
        # 平均半径
        pts = [om.MVector(mesh_fn.getPoint(item["vtx"], om.MSpace.kWorld)) for item in row]
        mid = sum(pts, om.MVector(0, 0, 0)) * (1.0 / len(pts))
        avg_radius = sum([(p - mid).length() for p in pts]) / len(pts)
        return avg_radius
    else:
        # 线状，左右两点距离
        left = row[0]["vtx"]
        right = row[-1]["vtx"]
        p1 = om.MVector(mesh_fn.getPoint(left, om.MSpace.kWorld))
        p2 = om.MVector(mesh_fn.getPoint(right, om.MSpace.kWorld))
        return (p1 - p2).length()


def get_signed_angle_between(v1, v2, up):
    """
    计算 v1 到 v2 的夹角（度），以 up 为法线确定正负。

    Args:
        v1 (om.MVector): 起始向量。
        v2 (om.MVector): 目标向量。
        up (om.MVector): 法线方向。

    Returns:
        float: 带符号角度（度）。
    """
    v1 = v1.normal()
    v2 = v2.normal()
    angle = math.degrees(math.acos(max(-1.0, min(1.0, v1 * v2))))
    cross = v1 ^ v2
    if (cross * up) <= 0:
        angle = -angle
    return angle


def get_mesh_fn(mesh):
    """获取MFnMesh对象"""
    sel_list = om.MSelectionList()
    sel_list.add(mesh)
    dag_path = sel_list.getDagPath(0)
    return om.MFnMesh(dag_path)


def get_profile_indices_by_uv_grid(uv_grid, row_idx):
    row = uv_grid[row_idx]
    left_id = row[0]["vtx"]
    right_id = row[-1]["vtx"]
    return left_id, right_id


def assign_existing_material(source_mesh, target_mesh):
    source_shapes = cmds.listRelatives(source_mesh, shapes=True, fullPath=True)
    if not source_shapes:
        logging.info("源 mesh 没有 shape 节点")
        return
    source_shape = source_shapes[0]
    shading_groups = cmds.listConnections(source_shape, type="shadingEngine")
    if not shading_groups:
        logging.info("源 mesh 没有分配材质")
        return
    shading_group = shading_groups[0]

    # 将目标 mesh 关联到 shading group
    cmds.sets(target_mesh, e=True, forceElement=shading_group)


def update_profile_orientation_and_twist(mesh, new_mesh, uv_grid, new_uv_grid, mid_points, curvename, utils):
    """
    更新 profile 曲线的根部方向、法线和整体扭转信息，并将 twist 曲线存入 utils。
    """
    # 根部 profile 的左右端点索引
    root_left_id, root_right_id = get_profile_indices_by_uv_grid(uv_grid, 0)
    new_root_left_id, new_root_right_id = get_profile_indices_by_uv_grid(new_uv_grid, 0)
    # 根部 profile 的向量
    root_vec = get_vec(mesh, root_left_id, root_right_id)
    new_root_vec = get_vec(new_mesh, new_root_left_id, new_root_right_id)
    # up 方向
    up = om.MVector(*mid_points[1]) - om.MVector(*mid_points[0])

    if root_vec.length() > 1e-6 and new_root_vec.length() > 1e-6:
        rot_angle = get_signed_angle_between(new_root_vec, root_vec, up)
        utils.set_profile_orientation(curvename, rot_angle)

    # 计算 twist 曲线
    twist_curve = get_profile_rotation_list_smooth(mesh, new_mesh, mid_points, len(mid_points))

    if twist_curve and twist_curve[-1][1] > 1.0:
        last_angle_norm = twist_curve[-1][1]
        last_accum_theta = (last_angle_norm - 0.5) * 360
        utils.set_profile_twist(curvename, last_accum_theta)
        twist_curve = get_profile_rotation_list_smooth(mesh, new_mesh, mid_points, len(mid_points))
    utils.setSingleGraph("twist", twist_curve)
    return twist_curve


def update_profile_value_by_v_shape(mesh, scale_curve, mid_points, uv_grid, core, utils, V_SHAPE_THRESHOLD=0.05):
    """
    根据V形状匹配结果，计算并设置profile_value。
    """
    result = v_shape_match_result(mesh, scale_curve, mid_points, V_SHAPE_THRESHOLD=V_SHAPE_THRESHOLD)
    profile_value = 0
    if result:
        core.Create.setProfileCurve(result)
        root_left_id, root_right_id = get_profile_indices_by_uv_grid(uv_grid, 0)
        root_mid_id, _ = get_profile_indices_by_uv_grid(uv_grid, 1)
        tip_left_id, tip_right_id = get_profile_indices_by_uv_grid(uv_grid, len(uv_grid) - 1)

        # 获取端点坐标
        root_left_pos = get_vertex_position(mesh, root_left_id)
        root_right_pos = get_vertex_position(mesh, root_right_id)
        root_mid_pos = get_vertex_position(mesh, root_mid_id)

        # 获取端点法线
        root_left_normal = get_vertex_normal(mesh, root_left_id)
        root_right_normal = get_vertex_normal(mesh, root_right_id)

        # 三点确定平面
        A = np.array(root_left_pos)
        B = np.array(root_right_pos)
        C = np.array(root_mid_pos)  # 根部中线点
        N = np.cross(B - A, C - A)
        N = N / np.linalg.norm(N)

        # 投影法线到平面
        def project_to_plane(V, N):
            return V - np.dot(V, N) * N

        nA_proj = project_to_plane(np.array(root_left_normal), N)
        nB_proj = project_to_plane(np.array(root_right_normal), N)
        U = B - A
        U = U / np.linalg.norm(U)
        V = np.cross(N, U)

        def to_2d(P, origin, U, V):
            return np.array([np.dot(P - origin, U), np.dot(P - origin, V)])

        A2 = to_2d(A, A, U, V)  # (0,0)
        B2 = to_2d(B, A, U, V)
        nA2 = np.array([np.dot(nA_proj, U), np.dot(nA_proj, V)])
        nB2 = np.array([np.dot(nB_proj, U), np.dot(nB_proj, V)])
        # 射线1: P = A2 + t1 * nA2
        # 射线2: Q = B2 + t2 * nB2
        # 求 t1, t2 使得 A2 + t1*nA2 = B2 + t2*nB2
        M = np.column_stack((nA2, -nB2))
        if np.linalg.matrix_rank(M) < 2:
            # 平行，不相交
            is_v_in = False
        else:
            t = np.linalg.lstsq(M, B2 - A2, rcond=None)[0]
            t1, t2 = t
            # 只考虑正向交点
            is_v_in = (t1 > 0) and (t2 > 0)
        profile_value = -0.65 if is_v_in else 0.65
        utils.setProfile(profile_value)
    else:
        default_v_shape = 0.5
        norm_mid_curve_positions = utils.get_normalized_curve_positions(mid_points)
        res = [(pos, default_v_shape) for pos in norm_mid_curve_positions]
        core.Create.setProfileCurve(res)

    return result, profile_value


def reverse_new_mesh_normals_if_needed(mesh, new_mesh, uv_grid, new_uv_grid):
    """
    检查新旧mesh四角端点法线方向是否一致，不一致则反转新mesh法线。
    """
    # 获取四角端点索引
    root_left_id, root_right_id = get_profile_indices_by_uv_grid(uv_grid, 0)
    new_root_left_id, new_root_right_id = get_profile_indices_by_uv_grid(new_uv_grid, 0)
    tip_left_id, tip_right_id = get_profile_indices_by_uv_grid(uv_grid, len(uv_grid) - 1)
    new_tip_left_id, new_tip_right_id = get_profile_indices_by_uv_grid(new_uv_grid, len(new_uv_grid) - 1)
    old_corners = [root_left_id, root_right_id, tip_left_id, tip_right_id]
    new_corners = [new_root_left_id, new_root_right_id, new_tip_left_id, new_tip_right_id]

    # 获取法线
    old_normals = [get_vertex_normal(mesh, idx) for idx in old_corners]
    new_normals = [get_vertex_normal(new_mesh, idx) for idx in new_corners]

    # 检查法线方向
    need_reverse = False
    for n1, n2 in zip(old_normals, new_normals):
        if n1 * n2 < 0:
            need_reverse = True
            break

    # 反转法线
    if need_reverse:
        logging.info("新mesh与原mesh端点法线方向不一致，反转新mesh法线")
        cmds.polyNormal(new_mesh, normalMode=0, userNormalMode=0, ch=0)
    return need_reverse
