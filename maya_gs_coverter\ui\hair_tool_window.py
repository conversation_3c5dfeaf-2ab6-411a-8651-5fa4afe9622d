from qtpy import QtWidgets, QtCore
from blade_client_reporter import get_reporter
import maya.cmds as cmds

from maya_gs_coverter.hair import process, simplify_curve_graph, generate_lod
import maya_gs_coverter.compatability_mode.converter as compatability_converter

from maya import OpenMayaUI as omui  # Maya main window
try:
    from shiboken2 import wrapInstance  # Maya 2022 (PySide2)
except Exception:  # fallback for other bindings if needed
    try:
        from shiboken6 import wrapInstance  # PySide6
    except Exception:
        wrapInstance = None


class HairToolWindow(QtWidgets.QDialog):
    """
    GS转换工具的Qt窗口。
    该模块直接依赖功能模块 maya_gs_coverter.hair，
    界面仅负责绑定按钮与展示进度，不更改任何业务逻辑。
    """

    def __init__(self, parent=None):
        super(HairToolWindow, self).__init__(parent)
        self.setObjectName("autoHairWin")
        self.setWindowTitle("毛发面片转换GS工具")
        self.setMinimumWidth(360)
        # 界面
        self._build_ui()

    # ---------------- UI Building ----------------
    def _build_ui(self):
        main = QtWidgets.QVBoxLayout(self)
        main.setContentsMargins(8, 8, 8, 8)
        main.setSpacing(8)

        # 顶部说明
        tip = QtWidgets.QLabel(
            "1.选中一个或多个毛发面片对象\n"
            "2.实验版本，数量较多时耗时较长！！！\n"
            "3.选中整个毛发时可自动进行拆分\n"
            "4.UV需要一定规范-长方形 从上到下 从根到尾\n"
            "5.若目标物体及父组未冻结生成结果可能有误"
        )
        tip.setWordWrap(True)
        main.addWidget(tip)

        # 是否逐顶点匹配
        self.vertex_match_cb = QtWidgets.QCheckBox("是否进行逐顶点匹配")
        main.addWidget(self.vertex_match_cb)

        # 标准转换
        self.process_btn = QtWidgets.QPushButton("标准转换")
        self.process_btn.clicked.connect(self.on_process)
        main.addWidget(self.process_btn)

        main.addWidget(self._hline())
        compat_btn = QtWidgets.QPushButton("兼容模式转换")
        compat_btn.clicked.connect(self.on_compat)
        main.addWidget(compat_btn)

        main.addWidget(self._hline())
        main.addWidget(self._title_label("-----------简化Graph-----------"))
        main.addWidget(self._center_label("请选中曲线后再点击简化"))

        graph_row = QtWidgets.QHBoxLayout()
        self.graph_combo = QtWidgets.QComboBox()
        self.graph_combo.addItems(["scale", "twist", "profile"])
        graph_row.addWidget(QtWidgets.QLabel("Graph类型"))
        graph_row.addWidget(self.graph_combo, 1)
        simp_btn = QtWidgets.QPushButton("简化Graph")
        simp_btn.clicked.connect(self.on_simplify)
        graph_row.addWidget(simp_btn)
        main.addLayout(graph_row)

        main.addWidget(self._hline())
        main.addWidget(self._title_label("-----------LOD生成-----------"))
        main.addWidget(self._center_label("选择曲线后点击生成"))

        lod_row = QtWidgets.QHBoxLayout()
        lod_row.addWidget(QtWidgets.QLabel("减面百分比"))
        self.percent_spin = QtWidgets.QSpinBox()
        self.percent_spin.setRange(0, 200)
        self.percent_spin.setSingleStep(10)
        self.percent_spin.setValue(80)
        lod_row.addWidget(self.percent_spin)
        gen_btn = QtWidgets.QPushButton("生成")
        gen_btn.clicked.connect(self.on_generate)
        lod_row.addWidget(gen_btn)
        main.addLayout(lod_row)

        # 进度区（初始隐藏）
        self.progress_container = QtWidgets.QWidget()
        v = QtWidgets.QVBoxLayout(self.progress_container)
        v.setContentsMargins(0, 0, 0, 0)
        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_label = QtWidgets.QLabel("已处理 0 / 0")
        self.progress_label.setAlignment(QtCore.Qt.AlignCenter)
        v.addWidget(self.progress_bar)
        v.addWidget(self.progress_label)
        self.progress_container.setVisible(False)
        main.addWidget(self.progress_container)

    def _hline(self):
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        return line

    def _title_label(self, text: str):
        lbl = QtWidgets.QLabel(text)
        lbl.setAlignment(QtCore.Qt.AlignCenter)
        return lbl

    def _center_label(self, text: str):
        lbl = QtWidgets.QLabel(text)
        lbl.setAlignment(QtCore.Qt.AlignCenter)
        return lbl

    # ---------------- Slots ----------------
    def on_process(self):
        # 显示进度区并重置
        self.progress_container.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("已处理 0 / 0")

        # 统计上报：构建
        with get_reporter(app_name="maya_gs_coverter", browser_name="maya2022") as api:
            api.report_count(event_name="build", action="build gs group", tool_name="cgame_avatar_factory")

        do_vertex_match = self.vertex_match_cb.isChecked()
        # 调用功能模块逻辑
        process(
            do_vertex_match=do_vertex_match,
            progress_bar=self.progress_bar,
            progress_text=self.progress_label,
        )
        self.progress_container.setVisible(False)

    def on_simplify(self):
        graph_type = self.graph_combo.currentText()
        simplify_curve_graph(graph_type)

    def on_generate(self):
        percent = int(self.percent_spin.value())
        selection = cmds.ls(sl=True)
        if not selection:
            cmds.warning("未选择任何对象！")
            return

        curves_to_process = set()
        for obj in selection:
            if cmds.objectType(obj, isType="transform"):
                children = cmds.listRelatives(obj, children=True, fullPath=True) or []
                for child in children:
                    if cmds.objectType(child, isType="nurbsCurve"):
                        parent = cmds.listRelatives(child, parent=True, fullPath=True)[0]
                        curves_to_process.add(parent)
                    elif cmds.objectType(child, isType="transform"):
                        shape = cmds.listRelatives(child, shapes=True, fullPath=True)
                        if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                            curves_to_process.add(child)
                shape = cmds.listRelatives(obj, shapes=True, fullPath=True)
                if shape and cmds.objectType(shape[0], isType="nurbsCurve"):
                    curves_to_process.add(obj)
                elif cmds.objectType(obj, isType="nurbsCurve"):
                    parent = cmds.listRelatives(obj, parent=True, fullPath=True)[0]
                    curves_to_process.add(parent)

        if not curves_to_process:
            cmds.warning("未找到可处理的曲线！")
            return

        for curve in curves_to_process:
            generate_lod(curve, percent)


    def on_compat(self):
        # 显示进度区并重置
        self.progress_container.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("已处理 0 / 0")

        # 执行兼容模式的批量转换（按标准转换相同的拆分与分组规则）
        compatability_converter.convert_selected_hair_cards_to_gs_cards_batch(
            progress_bar=self.progress_bar,
            progress_text=self.progress_label,
        )
        self.progress_container.setVisible(False)


def _get_maya_main_window():
    try:
        ptr = omui.MQtUtil.mainWindow()
        if ptr and wrapInstance:
            return wrapInstance(int(ptr), QtWidgets.QWidget)
    except Exception:
        pass
    return None


def show_window():
    # 统计上报：启动工具
    with get_reporter(app_name="maya_gs_coverter", browser_name="maya2022") as api:
        api.report_count(event_name="start", action="tool started", tool_name="cgame_avatar_factory")

    # 若已存在同名 Qt 窗口则先关闭
    try:
        for w in QtWidgets.QApplication.topLevelWidgets():
            if w.objectName() == "autoHairWin":
                w.close()
                w.deleteLater()
    except Exception:
        pass

    parent = _get_maya_main_window()
    global _hair_tool_win
    _hair_tool_win = HairToolWindow(parent=parent)
    # 作为 Maya 主窗口的工具窗口，始终浮在 Maya 之上（但不抢占其他应用）
    _hair_tool_win.setWindowFlag(QtCore.Qt.Tool, True)
    _hair_tool_win.show()
    _hair_tool_win.raise_()
    _hair_tool_win.activateWindow()

    return _hair_tool_win
