# Import built-in modules
import sys

# Import local modules
from maya_gs_coverter.constants import PACKAGE_NAME
from maya_gs_coverter.ui.hair_tool_window import show_window


def module_cleanup(module_name):
    """Cleanup module_name in sys.modules cache.

    Args:
        module_name (str, ModuleType): Module Name

    Raises:
        TypeError: invalid module_name
    """
    if module_name in sys.builtin_module_names:
        return

    pred = "%s." % module_name
    packages = [mod for mod in sys.modules if mod.startswith(pred)]
    packages += [module_name]
    for package in packages:
        module = sys.modules.get(package)
        if module is not None:
            del sys.modules[package]  # noqa:WPS420


def launch_window():
    module_cleanup(PACKAGE_NAME)
    show_window()
