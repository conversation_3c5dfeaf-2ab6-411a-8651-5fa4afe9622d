"""Contains information and properties pertaining to <PERSON>z package."""
name = "maya_gs_coverter"
authors = ["<PERSON>ong"]
uuid = "c692d7b8-cd7b-4ea4-8f36-7b934907e722"
description = "Convert regular type inserts to GS editable curves, still in experimental stage."
homepage = "https://git.woa.com/lightbox/internal/maya_gs_coverter"
tools = []
build_requires = []
private_build_requires = [
    "rez_builder-0",
    "setuptools_scm-1.15",
]
requires = [
    "python-3.7..4",
    "lightbox_config-1",
    "setuptools-41..71",
    "qtpy-1.11",
    "maya-2022..2024",
    "scipy-1..2",
    "maya_scripts_shelf-0",
    "blade_client_reporter-0",
]
TEST_COMMAND = "pytest-maya --cov=maya_gs_coverter --pyargs maya_gs_coverter/test"
# Internal variable used by `rezolve_me` for dev environment.
# Overrides `requires`. Uncomment this only if needed.
# dev_requires = requires + ["pytest-4.6", "pytest_cov-2.10"]
variants = [
    ["maya-2022", "pymel-1.2"],
    ["maya-2023", "pymel-1.4"],
]
tests = {
    "maya-2022": {
        "command": TEST_COMMAND,
        "requires": [
            "pytest_maya-0",
            "pytest-4.6",
            "pytest_cov-2.10",
            "maya-2022",
            "python-3.7",
        ],
    },
    "maya-2023": {
        "command": TEST_COMMAND,
        "requires": [
            "pytest_maya-0",
            "pytest-4.6",
            "pytest_cov-2.10",
            "maya-2023",
            "python-3.9",
        ],
    },
}

thm_actions = {
    "lm": {
        "command": "maya",
        "requires": ["maya-2022", "python-3.7"],
        "env": {
            "PYTHONPATH": {
                "action": "prepend",
                "value": "{this.root};",
            },
            "THM_MENU_CONFIG_PATH": {
                "action": "prepend",
                "value": "{this.root}/config/menu.yaml",
            },
            "THM_LOG_LEVEL": {
                "action": "set",
                "value": "DEBUG",
            },
        },
        "tox_options": {
            "passenv": "*",
            "description": "my custom action",
        },
    },
}


def commands():
    """Set up package."""
    env.PYTHONPATH.prepend("{this.root}/site-packages")  # noqa: F821
    env.THM_MENU_CONFIG_PATH.prepend("{this.root}/config/menu.yaml")  # noqa: F821
