# Import third-party modules
import maya.api.OpenMaya as om
import maya.cmds as cmds


def get_selected_mesh_transform():
    selection = cmds.ls(selection=True, type="transform")
    if not selection:
        return None
    return selection[0]


def get_mesh_shape_from_transform(mesh_transform):
    shapes = cmds.listRelatives(mesh_transform, shapes=True, type="mesh")
    if not shapes:
        return None
    return shapes[0]


def build_mesh_fn(mesh_shape):
    sel_list = om.MSelectionList()
    sel_list.add(mesh_shape)
    dag_path = sel_list.getDagPath(0)
    return om.MFnMesh(dag_path)


def get_current_uv_set_name(mesh_fn):
    try:
        return mesh_fn.currentUVSetName()
    except Exception:
        uv_sets = mesh_fn.getUVSetNames()
        return uv_sets[0] if uv_sets else "map1"


def get_uv_arrays(mesh_fn, uv_set):
    return mesh_fn.getUVs(uvSet=uv_set)


def compute_num_points_from_mesh(mesh_fn):
    # 根据当前面片的顶点数，取 int(顶点数/2) + 4
    return int(mesh_fn.numVertices / 2) + 4


def select_top_uv(u_arr, v_arr, threshold=0.01):
    """
    返回 UV 中顶部采样点。

    若最高两点的 V 差值不超过 threshold，则返回两点中点；否则返回最高点。

    Args:
        u_arr (list[float]): U 数组。
        v_arr (list[float]): V 数组。
        threshold (float, optional): 顶部两点视作同一高度的阈值。默认 0.01。

    Returns:
        tuple[float, float]: 顶部 (u, v)。
    """
    sorted_indices = sorted(range(len(v_arr)), key=lambda i: v_arr[i], reverse=True)
    if not sorted_indices:
        return (0.0, 0.0)
    if len(sorted_indices) >= 2:
        top1, top2 = sorted_indices[0], sorted_indices[1]
        if abs(v_arr[top1] - v_arr[top2]) <= threshold:
            return ((u_arr[top1] + u_arr[top2]) * 0.5, (v_arr[top1] + v_arr[top2]) * 0.5)
        else:
            return (u_arr[top1], v_arr[top1])
    else:
        idx_max = sorted_indices[0]
        return (u_arr[idx_max], v_arr[idx_max])


def select_bottom_uv(u_arr, v_arr, threshold=0.01):
    """
    返回 UV 中底部采样点。

    若最低两点的 V 差值小于 threshold，则返回两点中点；否则返回最低点。

    Args:
        u_arr (list[float]): U 数组。
        v_arr (list[float]): V 数组。
        threshold (float, optional): 底部两点视作同一高度的阈值。默认 0.01。

    Returns:
        tuple[float, float]: 底部 (u, v)。
    """
    sorted_indices = sorted(range(len(v_arr)), key=lambda i: v_arr[i])
    if not sorted_indices:
        return (0.0, 0.0)
    if len(sorted_indices) >= 2:
        bottom1, bottom2 = sorted_indices[0], sorted_indices[1]
        if abs(v_arr[bottom1] - v_arr[bottom2]) < threshold:
            return ((u_arr[bottom1] + u_arr[bottom2]) * 0.5, (v_arr[bottom1] + v_arr[bottom2]) * 0.5)
        else:
            return (u_arr[bottom1], v_arr[bottom1])
    else:
        idx_min = sorted_indices[0]
        return (u_arr[idx_min], v_arr[idx_min])


def uv_sample_at_index(i, num_points, u_center, v_min, v_max, top_uv, bottom_uv):
    """
    按从上到下的规则返回第 i 个采样的 (u, v)。首尾使用 top_uv/bottom_uv。

    Args:
        i (int): 采样序号。
        num_points (int): 采样总点数。
        u_center (float): 中心水平 U 值。
        v_min (float): V 最小值。
        v_max (float): V 最大值。
        top_uv (tuple[float, float]): 顶部 (u, v)。
        bottom_uv (tuple[float, float]): 底部 (u, v)。

    Returns:
        tuple[float, float]: (u, v)
    """
    if i == 0:
        return top_uv
    if i == num_points - 1:
        return bottom_uv
    v_range = v_max - v_min
    v = v_max - (float(i) / (num_points - 1)) * v_range
    return (u_center, v)


def find_point_at_uv_on_any_face(mesh_fn, uv_set, u, v, tolerance):
    """
    通过线性扫描所有面查找 (u, v) 对应的 3D 点，作为后备方案。

    Args:
        mesh_fn (om.MFnMesh): 网格函数集。
        uv_set (str): UV 集名称。
        u (float): U 值。
        v (float): V 值。
        tolerance (float): getPointAtUV 的容差。

    Returns:
        tuple[float, float, float] | None: 命中时返回 (x, y, z)，否则 None。
    """
    face_count = mesh_fn.numPolygons
    for fid in range(face_count):
        try:
            point = mesh_fn.getPointAtUV(fid, u, v, om.MSpace.kWorld, uv_set, tolerance)
            return (point.x, point.y, point.z)
        except Exception:
            continue
    return None


def create_curve_with_name(mesh_transform, degree, points):
    curve_name = f"{mesh_transform}_centerline_crv"
    cmds.curve(degree=degree, editPoint=points, name=curve_name)
    return curve_name
