## 0.1.10 (2025-09-11)

### Fix

- Fix some minor issues.

## 0.1.9 (2025-09-10)

### Fix

- lint-baseline.
- Refactor batch_convert_selected().
- Boundary condition detection.
- Reuse batch convert functions.
- Batch convert.
- Fix orientation calculation.
- Use point at 30% as root.
- fit uv and assign materials.
- Keep the tool window always floating above Maya without hijacking other applications.
- Refactor UI.
- Refactor(hair, compatibility_mode/converter): Remove duplicates and reuse the rotation/scale curve logic.
- Set widths for every segment.
- Handle UV horizontal flipping situations.
- Fit twist curve for every segment.
- Refactor.
- Fit orientation.
- Calculate new gs card width.
- Get normal at uv.
- preflight.
- Use google style docstring.
- Calculate section metrics.
- Refactor
- Find point and width at given uv.
- Optimize curve extraction performance.
- **WIP**: compatability converter.

## 0.1.8 (2025-08-18)

### Fix

- Fix twist angle calculation for tilted segments in curves

## 0.1.7 (2025-08-13)

### Fix

- add lod_generate

## 0.1.6 (2025-08-13)

### Fix

- lattice control

## 0.1.5 (2025-08-11)

### Fix

- match by cmds

## 0.1.4 (2025-08-08)

### Fix

- add progress_bar

## 0.1.3 (2025-08-08)

### Fix

- window  widthHeight
- add simplify_curve_graph
- package
- fix lint
- fix package.py
- remove unused import
- add custom builder

## 0.1.2 (2025-08-01)

### Fix

- package

## 0.1.1 (2025-07-31)

### Fix

- fix lint
- fix package.py
- remove unused import
- add custom builder

## 0.1.0 (2025-07-30)

### Fix

- remove reload

### Feat

- implement basic hair convert (quick & dirty for temp tool)
